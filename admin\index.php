<?php
// تم إزالة كود الديباغ بعد التأكد من حل المشكلة
?>
<?php
session_start();
include '../includes/auth_admin.php';
include '../includes/header.php';
renderHeader("لوحة المدير - الرصين", false, "index.php");
?>
<?php
include '../config/db.php';

// جلب إحصائيات شاملة للنظام
$stats = [
    'tickets' => ['total' => 0, 'open' => 0, 'closed' => 0, 'in_progress' => 0],
    'users' => ['total' => 0, 'admin' => 0, 'departments' => []],
    'maintenance' => ['total' => 0, 'today' => 0, 'completed' => 0, 'pending' => 0],
    'lab_status' => ['total' => 0, 'today' => 0],
    'devices' => ['total' => 0],
    'system' => ['uptime' => '', 'last_backup' => '']
];

// إحصائيات التذاكر
$tables_to_check = ['tickets', 'users', 'maintenance_reports', 'lab_status', 'devices'];
foreach ($tables_to_check as $table) {
    $check = $conn->query("SHOW TABLES LIKE '$table'");
    if (!$check || $check->num_rows == 0) {
        continue;
    }

    switch ($table) {
        case 'tickets':
            $res = $conn->query("SELECT COUNT(*) as total FROM tickets");
            if ($res) $stats['tickets']['total'] = $res->fetch_assoc()['total'];

            $res = $conn->query("SELECT COUNT(*) as open FROM tickets WHERE status = 'مفتوحة'");
            if ($res) $stats['tickets']['open'] = $res->fetch_assoc()['open'];

            $res = $conn->query("SELECT COUNT(*) as closed FROM tickets WHERE status = 'مغلقة'");
            if ($res) $stats['tickets']['closed'] = $res->fetch_assoc()['closed'];

            $res = $conn->query("SELECT COUNT(*) as progress FROM tickets WHERE status = 'قيد المعالجة'");
            if ($res) $stats['tickets']['in_progress'] = $res->fetch_assoc()['progress'];
            break;

        case 'users':
            $res = $conn->query("SELECT COUNT(*) as total FROM users");
            if ($res) $stats['users']['total'] = $res->fetch_assoc()['total'];

            $res = $conn->query("SELECT COUNT(*) as admin FROM users WHERE role = 'مدير'");
            if ($res) $stats['users']['admin'] = $res->fetch_assoc()['admin'];

            $res = $conn->query("SELECT role, COUNT(*) as count FROM users GROUP BY role");
            if ($res) {
                while ($row = $res->fetch_assoc()) {
                    $stats['users']['departments'][$row['role']] = $row['count'];
                }
            }
            break;

        case 'maintenance_reports':
            $res = $conn->query("SELECT COUNT(*) as total FROM maintenance_reports");
            if ($res) $stats['maintenance']['total'] = $res->fetch_assoc()['total'];

            $res = $conn->query("SELECT COUNT(*) as today FROM maintenance_reports WHERE DATE(created_at) = CURDATE()");
            if ($res) $stats['maintenance']['today'] = $res->fetch_assoc()['today'];

            $res = $conn->query("SELECT COUNT(*) as completed FROM maintenance_reports WHERE status = 'مكتملة'");
            if ($res) $stats['maintenance']['completed'] = $res->fetch_assoc()['completed'];

            $res = $conn->query("SELECT COUNT(*) as pending FROM maintenance_reports WHERE status != 'مكتملة'");
            if ($res) $stats['maintenance']['pending'] = $res->fetch_assoc()['pending'];
            break;

        case 'lab_status':
            $res = $conn->query("SELECT COUNT(*) as total FROM lab_status");
            if ($res) $stats['lab_status']['total'] = $res->fetch_assoc()['total'];

            $res = $conn->query("SELECT COUNT(*) as today FROM lab_status WHERE DATE(created_at) = CURDATE()");
            if ($res) $stats['lab_status']['today'] = $res->fetch_assoc()['today'];
            break;

        case 'devices':
            $res = $conn->query("SELECT COUNT(*) as total FROM devices");
            if ($res) $stats['devices']['total'] = $res->fetch_assoc()['total'];
            break;
    }
}

// معلومات النظام
$stats['system']['uptime'] = function_exists('sys_getloadavg') ? sys_getloadavg()[0] : 'غير متاح';
$stats['system']['last_backup'] = date('Y-m-d H:i:s');
?>

<style>
    body {
        background: linear-gradient(-45deg, #0D1117, #1E2124, #0F1319, #1A1C1F);
        background-size: 400% 400%;
        animation: gradientBG 15s ease infinite;
    }
    
    @keyframes gradientBG {
        0% {background-position: 0% 50%;}
        50% {background-position: 100% 50%;}
        100% {background-position: 0% 50%;}
    }
    
    .dashboard-container {
        padding: 30px 0;
    }
    
    .dashboard-title {
        color: #fff;
        text-shadow: 0 0 10px rgba(29, 161, 242, 0.5);
        margin-bottom: 30px;
    }
    
    .card-custom {
        background-color: #1E2124;
        border: none;
        border-radius: 15px;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
        height: 100%;
    }
    
    .card-custom:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(29, 161, 242, 0.4);
    }
    
    .card-custom h5 {
        color: #1DA1F2;
        font-weight: bold;
    }
    
    .card-custom p {
        color: #aaa;
    }
    
    .btn-outline-primary {
        color: #1DA1F2;
        border-color: #1DA1F2;
    }
    
    .btn-outline-primary:hover {
        background-color: #1DA1F2;
        color: white;
    }
</style>

<div class="dashboard-container">
    <div class="d-flex justify-content-end mb-3" style="max-width:900px;margin:0 auto;">
        <a href="/rasseem/logout.php" class="btn btn-outline-danger btn-lg" style="border-radius:30px;">
            <span style="font-size:1.2em;">🚪</span> تسجيل الخروج
        </a>
    </div>

    <h1 class="text-center dashboard-title">مرحبًا فاظل المياحي 👋</h1>
    <p class="lead text-center mb-5" style="color: #aaa">لوحة تحكم المدير - نظام الرصين</p>

    <!-- إحصائيات رئيسية -->
    <div class="row justify-content-center mb-4">
        <div class="col-md-3 mb-2">
            <div class="card text-center" style="background: #1DA1F2; color: #fff; border-radius: 15px;">
                <div class="card-body">
                    <div style="font-size:2em;">📬</div>
                    <h6>إجمالي التذاكر</h6>
                    <div style="font-size:2em; font-weight:bold;"><?= $stats['tickets']['total'] ?></div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-2">
            <div class="card text-center" style="background: #ffc107; color: #222; border-radius: 15px;">
                <div class="card-body">
                    <div style="font-size:2em;">⏳</div>
                    <h6>التذاكر المفتوحة</h6>
                    <div style="font-size:2em; font-weight:bold;"><?= $stats['tickets']['open'] ?></div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-2">
            <div class="card text-center" style="background: #28a745; color: #fff; border-radius: 15px;">
                <div class="card-body">
                    <div style="font-size:2em;">✅</div>
                    <h6>التذاكر المغلقة</h6>
                    <div style="font-size:2em; font-weight:bold;"><?= $stats['tickets']['closed'] ?></div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-2">
            <div class="card text-center" style="background: #6f42c1; color: #fff; border-radius: 15px;">
                <div class="card-body">
                    <div style="font-size:2em;">👥</div>
                    <h6>إجمالي المستخدمين</h6>
                    <div style="font-size:2em; font-weight:bold;"><?= $stats['users']['total'] ?></div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات ثانوية -->
    <div class="row justify-content-center mb-4">
        <div class="col-md-3 mb-2">
            <div class="card text-center" style="background: #17a2b8; color: #fff; border-radius: 15px;">
                <div class="card-body">
                    <div style="font-size:1.5em;">🔧</div>
                    <h6>تقارير الصيانة</h6>
                    <div style="font-size:1.5em; font-weight:bold;"><?= $stats['maintenance']['total'] ?></div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-2">
            <div class="card text-center" style="background: #fd7e14; color: #fff; border-radius: 15px;">
                <div class="card-body">
                    <div style="font-size:1.5em;">📊</div>
                    <h6>مواقف المخابر</h6>
                    <div style="font-size:1.5em; font-weight:bold;"><?= $stats['lab_status']['total'] ?></div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-2">
            <div class="card text-center" style="background: #20c997; color: #fff; border-radius: 15px;">
                <div class="card-body">
                    <div style="font-size:1.5em;">💻</div>
                    <h6>قاعدة الأجهزة</h6>
                    <div style="font-size:1.5em; font-weight:bold;"><?= $stats['devices']['total'] ?></div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-2">
            <div class="card text-center" style="background: #e83e8c; color: #fff; border-radius: 15px;">
                <div class="card-body">
                    <div style="font-size:1.5em;">🔄</div>
                    <h6>قيد المعالجة</h6>
                    <div style="font-size:1.5em; font-weight:bold;"><?= $stats['tickets']['in_progress'] ?></div>
                </div>
            </div>
        </div>
    </div>

    <!-- إدارة المستخدمين والصلاحيات -->
    <div class="row g-4 mb-4">
        <div class="col-12">
            <h3 class="text-center" style="color: #1DA1F2; margin-bottom: 2rem;">🔐 إدارة المستخدمين والصلاحيات</h3>
        </div>
        <div class="col-md-6 col-lg-3">
            <div class="card card-custom text-center p-4">
                <h5>👥 إدارة المستخدمين</h5>
                <p class="mb-3">إضافة / تعديل / حذف المستخدمين</p>
                <a href="/rasseem/admin/users/list.php" class="btn btn-outline-primary">إدارة المستخدمين</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-3">
            <div class="card card-custom text-center p-4">
                <h5>🔑 الصلاحيات</h5>
                <p class="mb-3">إدارة صلاحيات المستخدمين</p>
                <a href="/rasseem/admin/permissions.php" class="btn btn-outline-info">إدارة الصلاحيات</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-3">
            <div class="card card-custom text-center p-4">
                <h5>📊 تقارير المستخدمين</h5>
                <p class="mb-3">إحصائيات نشاط المستخدمين</p>
                <a href="/rasseem/admin/user_reports.php" class="btn btn-outline-secondary">عرض التقارير</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-3">
            <div class="card card-custom text-center p-4">
                <h5>🔒 سجل الدخول</h5>
                <p class="mb-3">مراقبة عمليات تسجيل الدخول</p>
                <a href="/rasseem/admin/login_logs.php" class="btn btn-outline-warning">عرض السجل</a>
            </div>
        </div>
    </div>

    <!-- إدارة التذاكر والدعم -->
    <div class="row g-4 mb-4">
        <div class="col-12">
            <h3 class="text-center" style="color: #28a745; margin-bottom: 2rem;">🎫 إدارة التذاكر والدعم الفني</h3>
        </div>
        <div class="col-md-6 col-lg-3">
            <div class="card card-custom text-center p-4">
                <h5>📩 جميع التذاكر</h5>
                <p class="mb-3">مشاهدة وإدارة جميع التذاكر</p>
                <a href="/rasseem/admin/tickets/all.php" class="btn btn-outline-success">عرض التذاكر</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-3">
            <div class="card card-custom text-center p-4">
                <h5>⚡ التذاكر العاجلة</h5>
                <p class="mb-3">التذاكر عالية الأولوية</p>
                <a href="/rasseem/admin/tickets/urgent.php" class="btn btn-outline-danger">التذاكر العاجلة</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-3">
            <div class="card card-custom text-center p-4">
                <h5>📈 إحصائيات التذاكر</h5>
                <p class="mb-3">تقارير وإحصائيات مفصلة</p>
                <a href="/rasseem/admin/tickets/stats.php" class="btn btn-outline-info">عرض الإحصائيات</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-3">
            <div class="card card-custom text-center p-4">
                <h5>🔄 توزيع التذاكر</h5>
                <p class="mb-3">توزيع التذاكر على الأقسام</p>
                <a href="/rasseem/admin/tickets/distribute.php" class="btn btn-outline-primary">توزيع التذاكر</a>
            </div>
        </div>
    </div>

    <!-- إدارة الأقسام والوحدات -->
    <div class="row g-4 mb-4">
        <div class="col-12">
            <h3 class="text-center" style="color: #6f42c1; margin-bottom: 2rem;">🏢 إدارة الأقسام والوحدات</h3>
        </div>
        <div class="col-md-6 col-lg-3">
            <div class="card card-custom text-center p-4">
                <h5>🧪 إدارة المخابر</h5>
                <p class="mb-3">مراقبة أنشطة المخابر</p>
                <a href="/rasseem/admin/departments/lab.php" class="btn btn-outline-primary">إدارة المخابر</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-3">
            <div class="card card-custom text-center p-4">
                <h5>⚙️ إدارة الهندسة</h5>
                <p class="mb-3">مراقبة قسم الهندسة</p>
                <a href="/rasseem/admin/departments/engineering.php" class="btn btn-outline-info">إدارة الهندسة</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-3">
            <div class="card card-custom text-center p-4">
                <h5>🔧 إدارة الصيانة</h5>
                <p class="mb-3">مراقبة قسم الصيانة</p>
                <a href="/rasseem/admin/departments/maintenance.php" class="btn btn-outline-warning">إدارة الصيانة</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-3">
            <div class="card card-custom text-center p-4">
                <h5>📱 إدارة الجوال</h5>
                <p class="mb-3">مراقبة إدارة الجوال</p>
                <a href="/rasseem/admin/departments/mobile.php" class="btn btn-outline-success">إدارة الجوال</a>
            </div>
        </div>
    </div>

    <!-- إدارة النظام والتقارير -->
    <div class="row g-4 mb-4">
        <div class="col-12">
            <h3 class="text-center" style="color: #dc3545; margin-bottom: 2rem;">⚙️ إدارة النظام والتقارير</h3>
        </div>
        <div class="col-md-6 col-lg-3">
            <div class="card card-custom text-center p-4">
                <h5>📊 لوحة المعلومات</h5>
                <p class="mb-3">إحصائيات شاملة للنظام</p>
                <a href="/rasseem/admin/dashboard.php" class="btn btn-outline-primary">لوحة المعلومات</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-3">
            <div class="card card-custom text-center p-4">
                <h5>📋 التقارير العامة</h5>
                <p class="mb-3">تقارير شاملة للنظام</p>
                <a href="/rasseem/admin/reports/general.php" class="btn btn-outline-info">عرض التقارير</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-3">
            <div class="card card-custom text-center p-4">
                <h5>💾 النسخ الاحتياطي</h5>
                <p class="mb-3">إدارة النسخ الاحتياطية</p>
                <a href="/rasseem/admin/backup.php" class="btn btn-outline-warning">النسخ الاحتياطي</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-3">
            <div class="card card-custom text-center p-4">
                <h5>🔧 إعدادات النظام</h5>
                <p class="mb-3">تكوين إعدادات النظام</p>
                <a href="/rasseem/admin/settings.php" class="btn btn-outline-secondary">الإعدادات</a>
            </div>
        </div>
    </div>

    <!-- إدارة قواعد البيانات والأجهزة -->
    <div class="row g-4 mb-4">
        <div class="col-12">
            <h3 class="text-center" style="color: #17a2b8; margin-bottom: 2rem;">💻 إدارة قواعد البيانات والأجهزة</h3>
        </div>
        <div class="col-md-6 col-lg-3">
            <div class="card card-custom text-center p-4">
                <h5>🗄️ قاعدة البيانات</h5>
                <p class="mb-3">إدارة قاعدة البيانات</p>
                <a href="/rasseem/admin/database.php" class="btn btn-outline-primary">إدارة قاعدة البيانات</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-3">
            <div class="card card-custom text-center p-4">
                <h5>💻 قاعدة الأجهزة</h5>
                <p class="mb-3">إدارة جميع الأجهزة</p>
                <a href="/rasseem/admin/devices.php" class="btn btn-outline-info">قاعدة الأجهزة</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-3">
            <div class="card card-custom text-center p-4">
                <h5>🌐 إدارة الشبكات</h5>
                <p class="mb-3">مراقبة الشبكات والاتصالات</p>
                <a href="/rasseem/admin/networks.php" class="btn btn-outline-success">إدارة الشبكات</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-3">
            <div class="card card-custom text-center p-4">
                <h5>📡 مراقبة الأداء</h5>
                <p class="mb-3">مراقبة أداء النظام</p>
                <a href="/rasseem/admin/monitoring.php" class="btn btn-outline-warning">مراقبة الأداء</a>
            </div>
        </div>
    </div>

    <!-- أدوات إضافية -->
    <div class="row g-4 mb-4">
        <div class="col-12">
            <h3 class="text-center" style="color: #fd7e14; margin-bottom: 2rem;">🛠️ أدوات إضافية</h3>
        </div>
        <div class="col-md-6 col-lg-3">
            <div class="card card-custom text-center p-4">
                <h5>📧 إرسال إشعارات</h5>
                <p class="mb-3">إرسال إشعارات للمستخدمين</p>
                <a href="/rasseem/admin/notifications.php" class="btn btn-outline-primary">إرسال إشعارات</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-3">
            <div class="card card-custom text-center p-4">
                <h5>📅 جدولة المهام</h5>
                <p class="mb-3">جدولة المهام التلقائية</p>
                <a href="/rasseem/admin/scheduler.php" class="btn btn-outline-info">جدولة المهام</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-3">
            <div class="card card-custom text-center p-4">
                <h5>🔍 البحث المتقدم</h5>
                <p class="mb-3">بحث متقدم في النظام</p>
                <a href="/rasseem/admin/search.php" class="btn btn-outline-success">البحث المتقدم</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-3">
            <div class="card card-custom text-center p-4">
                <h5>📊 تحليل البيانات</h5>
                <p class="mb-3">تحليل وإحصائيات متقدمة</p>
                <a href="/rasseem/admin/analytics.php" class="btn btn-outline-warning">تحليل البيانات</a>
            </div>
        </div>
    </div>

    <!-- معلومات النظام -->
    <div class="row g-4 mb-4">
        <div class="col-12">
            <div class="card" style="background-color: #1E2124; border: none; border-radius: 15px;">
                <div class="card-header" style="background-color: #2D3035; border-radius: 15px 15px 0 0;">
                    <h5 class="text-center" style="color: #1DA1F2; margin: 0;">📊 معلومات النظام</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <h6 style="color: #1DA1F2;">إجمالي الأقسام</h6>
                            <p style="color: #E5E5E5; font-size: 1.2em; font-weight: bold;"><?= count($stats['users']['departments']) ?></p>
                        </div>
                        <div class="col-md-3">
                            <h6 style="color: #28a745;">التذاكر النشطة</h6>
                            <p style="color: #E5E5E5; font-size: 1.2em; font-weight: bold;"><?= $stats['tickets']['open'] + $stats['tickets']['in_progress'] ?></p>
                        </div>
                        <div class="col-md-3">
                            <h6 style="color: #ffc107;">الصيانة المعلقة</h6>
                            <p style="color: #E5E5E5; font-size: 1.2em; font-weight: bold;"><?= $stats['maintenance']['pending'] ?></p>
                        </div>
                        <div class="col-md-3">
                            <h6 style="color: #17a2b8;">آخر نسخة احتياطية</h6>
                            <p style="color: #E5E5E5; font-size: 0.9em;"><?= $stats['system']['last_backup'] ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
include '../includes/footer.php';
renderFooter();
?>