<?php
session_start();
include '../../includes/auth_admin.php';
include '../../includes/header.php';
renderHeader("إضافة تذكرة جديدة", false, "all.php");
include '../../includes/ticket_nav.php';
?>
<style>
    body {
        background: linear-gradient(-45deg, #0D1117, #1E2124, #0F1319, #1A1C1F);
        background-size: 400% 400%;
        animation: gradientBG 15s ease infinite;
    }
    @keyframes gradientBG {
        0% {background-position: 0% 50%;}
        50% {background-position: 100% 50%;}
        100% {background-position: 0% 50%;}
    }
    .ticket-card {
        max-width: 500px;
        margin: 50px auto 0 auto;
        background: #23272b;
        border-radius: 18px;
        box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        padding: 35px 30px 30px 30px;
        color: #fff;
        position: relative;
        overflow: hidden;
    }
    .ticket-card:before {
        content: "";
        position: absolute;
        top: -60px;
        right: -60px;
        width: 180px;
        height: 180px;
        background: linear-gradient(135deg, #1DA1F2 0%, #0d6efd 100%);
        opacity: 0.15;
        border-radius: 50%;
        z-index: 0;
    }
    .ticket-card h2 {
        font-weight: bold;
        color: #1DA1F2;
        margin-bottom: 25px;
        z-index: 1;
        position: relative;
    }
    .ticket-card label {
        color: #b5b5b5;
        font-weight: 500;
    }
    .ticket-card .form-control,
    .ticket-card .form-select {
        background: #181a1b;
        color: #fff;
        border: 1px solid #222;
        border-radius: 8px;
    }
    .ticket-card .form-control:focus,
    .ticket-card .form-select:focus {
        border-color: #1DA1F2;
        box-shadow: 0 0 0 0.2rem rgba(29,161,242,.25);
    }
    .ticket-card .btn-success {
        background: linear-gradient(90deg, #1DA1F2 0%, #0d6efd 100%);
        border: none;
        font-weight: bold;
        border-radius: 30px;
        box-shadow: 0 4px 15px rgba(29, 161, 242, 0.2);
        transition: all 0.3s;
    }
    .ticket-card .btn-success:hover {
        background: linear-gradient(90deg, #0d6efd 0%, #1DA1F2 100%);
        transform: scale(1.04);
    }
    .ticket-card .icon {
        font-size: 2.5rem;
        color: #1DA1F2;
        margin-bottom: 10px;
        display: block;
        text-align: center;
    }
</style>
<div class="ticket-card">
    <div class="icon">➕</div>
    <h2 class="text-center mb-4">إضافة تذكرة جديدة</h2>
    <form method="POST" action="add_handler.php" enctype="multipart/form-data">
        <div class="mb-3">
            <label for="image" class="form-label">إرفاق صورة (اختياري)</label>
            <input type="file" class="form-control" id="image" name="image" accept="image/*">
        </div>
        <div class="mb-3">
            <label for="description" class="form-label">وصف المشكلة</label>
            <textarea class="form-control" id="description" name="description" required rows="2" placeholder="اكتب وصف مختصر للمشكلة..."></textarea>
        </div>
        <div class="mb-3">
            <label for="content" class="form-label">مضمون التكت</label>
            <textarea class="form-control" id="content" name="content" required rows="4" placeholder="اكتب تفاصيل التذكرة بشكل كامل..."></textarea>
        </div>
        <div class="mb-3">
            <label for="status" class="form-label">نوع التكت</label>
            <select class="form-select" id="status" name="status" required>
                <option value="مفتوحة">مفتوحة</option>
                <option value="مغلقة">مغلقة</option>
            </select>
        </div>
        <div class="mb-3">
            <label for="forwarded_to" class="form-label">توجيه التذكرة إلى قسم</label>
            <select class="form-select" id="forwarded_to" name="forwarded_to">
                <option value="" selected>غير موجهة</option>
                <option value="الإدارة العامة">الإدارة العامة</option>
                <option value="المخابر">المخابر</option>
                <option value="إدارة الجوال">إدارة الجوال</option>
                <option value="الهندسة">الهندسة</option>
                <option value="الصيانة">الصيانة</option>
            </select>
        </div>
        <input type="hidden" id="source" name="source" value="<?php
            // تحديد المصدر تلقائياً حسب الصفحة
            if (strpos($_SERVER['PHP_SELF'], '/dashboard/') !== false) {
                echo 'الإدارة العامة';
            } elseif (strpos($_SERVER['PHP_SELF'], '/admin/') !== false) {
                echo 'مدير';
            } elseif (strpos($_SERVER['PHP_SELF'], '/lab/') !== false) {
                echo 'المخابر';
            } else {
                echo 'غير محدد';
            }
        ?>">
        <button type="submit" class="btn btn-success w-100">حفظ التذكرة</button>
    </form>
</div>
<?php
include '../../includes/footer.php';
renderFooter();
?>