<?php
session_start();
include '../../includes/auth_admin.php';
include '../../config/db.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $description = $_POST['description'];
    $content = isset($_POST['content']) ? $_POST['content'] : null;
    $source = $_POST['source'];
    $status = $_POST['status'];
    $forwarded_to = isset($_POST['forwarded_to']) && $_POST['forwarded_to'] !== "" ? $_POST['forwarded_to'] : null;

    // رفع الصورة إن وجدت
    $image_name = null;
    if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
        $img_dir = '../../assets/images/tickets/';
        if (!is_dir($img_dir)) {
            mkdir($img_dir, 0777, true);
        }
        $ext = pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION);
        $image_name = uniqid('ticket_') . '.' . $ext;
        $target = $img_dir . $image_name;
        move_uploaded_file($_FILES['image']['tmp_name'], $target);
    }

    $stmt = $conn->prepare("INSERT INTO tickets (description, content, source, status, image, forwarded_to) VALUES (?, ?, ?, ?, ?, ?)");
    $stmt->bind_param("ssssss", $description, $content, $source, $status, $image_name, $forwarded_to);

    if ($stmt->execute()) {
        $role = isset($_SESSION['user']['role']) ? $_SESSION['user']['role'] : '';
        if ($role === 'مدير') {
            $redirect = '/rasseem/admin/tickets/all.php';
        } elseif ($role === 'الإدارة العامة') {
            $redirect = '/rasseem/dashboard/tickets/all.php';
        } elseif ($role === 'المخابر') {
            $redirect = '/rasseem/lab/tickets/all.php';
        } else {
            $redirect = '/rasseem/admin/tickets/all.php';
        }
        echo "<script>alert('تمت إضافة التذكرة بنجاح'); window.location.href='$redirect';</script>";
        exit();
    } else {
        echo "<script>alert('حدث خطأ أثناء إضافة التذكرة: <?= $stmt->error ?>'); window.location.href='add.php';</script>";
        exit();
    }
} else {
    header("Location: all.php");
    exit();
}
?>