<?php
session_start();
include '../../includes/auth_admin.php';
include '../../includes/header.php';
renderHeader("كل التذاكر", false, "/rasseem/admin/index.php");
include '../../includes/ticket_nav.php';
include '../../config/db.php';

// جلب التذاكر من قاعدة البيانات
$tickets = [];
$sql = "SELECT id, description, source, status FROM tickets ORDER BY id DESC";
$result = $conn->query($sql);
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $tickets[] = $row;
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>كل التذاكر - المدير</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css"  rel="stylesheet">
    <style>
        body {
            background: linear-gradient(-45deg, #0D1117, #1E2124, #0F1319, #1A1C1F);
            color: #E5E5E5;
        }
        .table-dark th {
            background-color: #1A1C1F;
        }
    </style>
</head>
<body>
<div class="container py-4">
    <h3>📬 كل التذاكر</h3>
    <a href="add.php" class="btn btn-success mb-3">➕ إضافة تذكرة جديدة</a>
    <table class="table table-striped table-dark mt-3">
        <thead>
            <tr>
                <th>#</th>
                <th>الوصف</th>
                <th>المصدر</th>
                <th>الحالة</th>
                <th>الإجراءات</th>
                <th>طباعة</th>
            </tr>
        </thead>
        <tbody>
            <?php if (count($tickets) > 0): ?>
                <?php foreach ($tickets as $ticket): ?>
                    <tr>
                        <td><?= $ticket['id'] ?></td>
                        <td><?= htmlspecialchars($ticket['description']) ?></td>
                        <td><?= htmlspecialchars($ticket['source']) ?></td>
                        <td>
                            <?php if ($ticket['status'] == 'مفتوحة'): ?>
                                <span class="badge bg-warning">مفتوحة</span>
                            <?php elseif ($ticket['status'] == 'مغلقة'): ?>
                                <span class="badge bg-success">مغلقة</span>
                            <?php else: ?>
                                <span class="badge bg-secondary"><?= htmlspecialchars($ticket['status']) ?></span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <a href="view.php?id=<?= $ticket['id'] ?>" class="btn btn-sm btn-primary">عرض</a>
                        </td>
                        <td>
                            <a href="print.php?id=<?= $ticket['id'] ?>" class="btn btn-sm btn-info" target="_blank">طباعة</a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php else: ?>
                <tr>
                    <td colspan="6" class="text-center">لا توجد تذاكر حالياً</td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script> 
</body>
</html>