<?php
session_start();
include '../../includes/auth_admin.php';
include '../../config/db.php';

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    echo "<script>alert('معرّف التذكرة غير صالح'); window.location.href='all.php';</script>";
    exit();
}

$id = intval($_GET['id']);

// فقط المدير يمكنه الحذف
if ($_SESSION['user']['role'] !== 'مدير') {
    echo "<script>alert('غير مصرح لك بحذف التذكرة'); window.location.href='all.php';</script>";
    exit();
}

$stmt = $conn->prepare("DELETE FROM tickets WHERE id = ?");
$stmt->bind_param("i", $id);

if ($stmt->execute()) {
    echo "<script>alert('تم حذف التذكرة بنجاح'); window.location.href='all.php';</script>";
    exit();
} else {
    echo "<script>alert('حدث خطأ أثناء الحذف'); window.location.href='all.php';</script>";
    exit();
}
?>