<?php
session_start();
include '../../includes/auth_admin.php';
include '../../config/db.php';

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    die("معرّف التذكرة غير صالح.");
}
$ticket_id = intval($_GET['id']);
$stmt = $conn->prepare("SELECT * FROM tickets WHERE id = ?");
$stmt->bind_param("i", $ticket_id);
$stmt->execute();
$ticket = $stmt->get_result()->fetch_assoc();
if (!$ticket) {
    die("التذكرة غير موجودة.");
}

// جلب الردود
$replies = [];
$res = $conn->prepare("SELECT * FROM ticket_replies WHERE ticket_id = ? ORDER BY created_at ASC");
$res->bind_param("i", $ticket_id);
$res->execute();
$replies_result = $res->get_result();
while ($row = $replies_result->fetch_assoc()) {
    $replies[] = $row;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>طباعة التذكرة</title>
    <style>
        body { font-family: "Cairo", "Tajawal", Arial, sans-serif; background: #fff; color: #222; }
        .print-container { max-width: 700px; margin: 30px auto; background: #fff; border: 2px solid #0d6efd; border-radius: 12px; padding: 30px; }
        h2 { color: #0d6efd; text-align: center; }
        .label { font-weight: bold; color: #0d6efd; margin-top: 15px; }
        .value { background: #f5f7fa; border-radius: 6px; padding: 8px 12px; border: 1px solid #e3e6ea; margin-bottom: 10px; }
        .badge { font-size: 1rem; padding: 6px 16px; border-radius: 20px; }
        .reply { background: #f5f7fa; border-radius: 8px; margin-bottom: 10px; padding: 10px; border: 1px solid #e3e6ea; }
        .reply .name { color: #0d6efd; font-weight: bold; }
        .reply .date { color: #888; font-size: 0.9em; }
        .ticket-image { display: block; margin: 18px auto 18px auto; max-width: 350px; border-radius: 8px; border: 1px solid #e3e6ea; }
        @media print {
            .no-print { display: none; }
            .print-container { border: none; }
        }
    </style>
</head>
<body>
<div class="print-container">
    <?php if (!empty($ticket['image'])): ?>
        <img src="../../assets/images/tickets/<?= htmlspecialchars($ticket['image']) ?>" alt="صورة التذكرة" class="ticket-image">
    <?php endif; ?>
    <div class="label">مضمون التكت:</div>
    <div class="value"><?= isset($ticket['content']) ? nl2br(htmlspecialchars($ticket['content'])) : '<span class="text-muted">غير متوفر</span>' ?></div>
    <div class="label">القسم الموجه إليه:</div>
    <div class="value"><?= $ticket['forwarded_to'] ? htmlspecialchars($ticket['forwarded_to']) : '<span class="text-muted">غير موجهة</span>' ?></div>
    <div class="label">الردود:</div>
    <?php if (count($replies) > 0): ?>
        <?php foreach ($replies as $reply): ?>
            <div class="reply">
                <span class="name"><?= htmlspecialchars($reply['ar_name']) ?>:</span>
                <span class="date"><?= $reply['created_at'] ?></span>
                <div><?= nl2br(htmlspecialchars($reply['reply'])) ?></div>
            </div>
        <?php endforeach; ?>
    <?php else: ?>
        <div class="value">لا توجد ردود بعد.</div>
    <?php endif; ?>
    <div class="no-print" style="text-align:center;margin-top:30px;">
        <button onclick="window.print()" class="btn btn-primary" style="padding:10px 30px;font-size:1.1em;border-radius:30px;">طباعة</button>
    </div>
</div>
</body>
</html>