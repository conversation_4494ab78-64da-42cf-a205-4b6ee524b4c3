<?php
include '../../includes/auth_admin.php';
include '../../config/db.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $conn->real_escape_string($_POST['username']);
    $password = password_hash($_POST['password'], PASSWORD_DEFAULT);
    $role = $conn->real_escape_string($_POST['role']);

    $sql = "INSERT INTO users (username, password, role) VALUES ('$username', '$password', '$role')";

    if ($conn->query($sql) === TRUE) {
        echo "<script>alert('تم إضافة المستخدم بنجاح'); window.location.href='add.php';</script>";
    } else {
        echo "<script>alert('حدث خطأ أثناء الإضافة'); window.location.href='add.php';</script>";
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>إضافة مستخدم - المدير</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css"  rel="stylesheet">
    <style>
        body {
            background: linear-gradient(-45deg, #0D1117, #1E2124, #0F1319, #1A1C1F);
            color: #E5E5E5;
        }

        .form-container {
            max-width: 600px;
            margin: 100px auto;
            padding: 30px;
            background-color: #1E2124;
            border-radius: 15px;
            box-shadow: 0 0 20px rgba(29, 161, 242, 0.2);
        }

        .btn-submit {
            background-color: #1DA1F2;
            border: none;
            color: white;
            border-radius: 30px;
        }

        .btn-submit:hover {
            background-color: #0d6efd;
        }
    </style>
</head>
<body>

<div class="form-container mx-auto">
    <h4 class="mb-4 text-center">➕ إضافة مستخدم جديد</h4>
    <form method="POST">
        <div class="mb-3">
            <label for="username" class="form-label">اسم المستخدم</label>
            <input type="text" class="form-control bg-dark text-white" id="username" name="username" required placeholder="مثال: lab_user1">
        </div>

        <div class="mb-3">
            <label for="password" class="form-label">كلمة المرور</label>
            <input type="password" class="form-control bg-dark text-white" id="password" name="password" required placeholder="أدخل كلمة المرور">
        </div>

        <div class="mb-3">
            <label for="role" class="form-label">الصلاحية</label>
            <select class="form-select bg-dark text-white" id="role" name="role" required>
                <option value="" disabled selected>اختر الصلاحية</option>
                <option value="مدير">مدير</option>
                <option value="إدارة الجوال">إدارة الجوال</option>
                <option value="المخابر">المخابر</option>
                <option value="الإدارة العامة">الإدارة العامة</option>
                <option value="الهندسة">الهندسة</option>
                <option value="الصيانة">الصيانة</option>
            </select>
        </div>

        <button type="submit" class="btn btn-submit w-100">إضافة المستخدم</button>
    </form>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script> 
</body>
</html>