<?php
session_start();
include '../../includes/auth_admin.php';
include '../../includes/header.php';
renderHeader("إضافة مستخدم جديد", false, "list.php");
?>
<div class="d-flex justify-content-end mb-3" style="max-width:600px;margin:0 auto;">
    <a href="list.php" class="btn btn-outline-info btn-lg" style="border-radius:30px;">
        <span style="font-size:1.2em;">⬅️</span> الرجوع لقائمة المستخدمين
    </a>
    <a href="/rasseem/admin/index.php" class="btn btn-outline-primary btn-lg ms-2" style="border-radius:30px;">
        <span style="font-size:1.2em;">🏠</span> الصفحة الرئيسية
    </a>
</div>
<style>
    .add-user-card {
        max-width: 500px;
        margin: 40px auto 0 auto;
        background: #23272b;
        border-radius: 18px;
        box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.25);
        padding: 35px 30px 30px 30px;
        color: #fff;
        position: relative;
        overflow: hidden;
    }
    .add-user-card:before {
        content: "";
        position: absolute;
        top: -60px;
        right: -60px;
        width: 180px;
        height: 180px;
        background: linear-gradient(135deg, #1DA1F2 0%, #0d6efd 100%);
        opacity: 0.13;
        border-radius: 50%;
        z-index: 0;
    }
    .add-user-card h4 {
        color: #1DA1F2;
        font-weight: bold;
        margin-bottom: 25px;
        z-index: 1;
        position: relative;
    }
    .add-user-card label {
        color: #b5b5b5;
        font-weight: 500;
    }
    .add-user-card .form-control,
    .add-user-card .form-select {
        background: #181a1b;
        color: #fff;
        border: 1px solid #222;
        border-radius: 8px;
    }
    .add-user-card .form-control:focus,
    .add-user-card .form-select:focus {
        border-color: #1DA1F2;
        box-shadow: 0 0 0 0.2rem rgba(29,161,242,.25);
    }
    .add-user-card .btn-success {
        background: linear-gradient(90deg, #1DA1F2 0%, #0d6efd 100%);
        border: none;
        font-weight: bold;
        border-radius: 30px;
        box-shadow: 0 4px 15px rgba(29, 161, 242, 0.2);
        transition: all 0.3s;
    }
    .add-user-card .btn-success:hover {
        background: linear-gradient(90deg, #0d6efd 0%, #1DA1F2 100%);
        transform: scale(1.04);
    }
</style>
<div class="add-user-card">
    <h4 class="mb-4 text-center">➕ إضافة مستخدم جديد</h4>
    <form method="POST" action="add_handler.php">
        <div class="mb-3">
            <label for="ar_name" class="form-label">الاسم بالعربية</label>
            <input type="text" class="form-control" id="ar_name" name="ar_name" required placeholder="مثال: أحمد محمد">
        </div>
        <div class="mb-3">
            <label for="username" class="form-label">اسم المستخدم</label>
            <input type="text" class="form-control" id="username" name="username" required placeholder="مثال: ahmed_mohamed">
        </div>
        <div class="mb-3">
            <label for="password" class="form-label">كلمة المرور</label>
            <input type="password" class="form-control" id="password" name="password" required placeholder="أدخل كلمة المرور">
        </div>
        <div class="mb-3">
            <label for="role" class="form-label">الصلاحية</label>
            <select class="form-select" id="role" name="role" required>
                <option value="" disabled selected>اختر الصلاحية</option>
                <option value="مدير">مدير</option>
                <option value="إدارة الجوال">إدارة الجوال</option>
                <option value="المخابر">المخابر</option>
                <option value="الإدارة العامة">الإدارة العامة</option>
                <option value="الهندسة">الهندسة</option>
                <option value="الصيانة">الصيانة</option>
            </select>
        </div>
        <button type="submit" class="btn btn-success w-100">إضافة المستخدم</button>
    </form>
</div>
<?php
include '../../includes/footer.php';
renderFooter();
?>