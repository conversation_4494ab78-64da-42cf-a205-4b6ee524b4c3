<?php
session_start();
include '../../includes/auth_admin.php';
include '../../config/db.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'];
    $password = password_hash($_POST['password'], PASSWORD_DEFAULT);
    $role = $_POST['role'];
    $ar_name = $_POST['ar_name'];

    // Use prepared statement to prevent SQL injection
    $sql = "INSERT INTO users (username, password, role, ar_name) VALUES (?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);
    
    if ($stmt === false) {
        error_log("SQL prepare error: " . $conn->error);
        die("حدث خطأ في النظام، يرجى المحاولة لاحقاً");
    }
    
    $stmt->bind_param("ssss", $username, $password, $role, $ar_name);
    
    if ($stmt->execute()) {
        echo "<script>alert('تم إضافة المستخدم بنجاح'); window.location.href='add.php';</script>";
        exit();
    } else {
        error_log("SQL execute error: " . $stmt->error);
        echo "<script>alert('حدث خطأ أثناء الإضافة'); window.location.href='add.php';</script>";
        exit();
    }
}
?>