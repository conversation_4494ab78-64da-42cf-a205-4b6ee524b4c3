<?php
session_start();
include '../../includes/auth_admin.php';
include '../../config/db.php';

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    echo "<script>alert('معرّف المستخدم غير صالح'); window.location.href='list.php';</script>";
    exit();
}

$id = intval($_GET['id']);

// لا تسمح بحذف نفسك
if ($_SESSION['user']['id'] == $id) {
    echo "<script>alert('لا يمكنك حذف حسابك الشخصي!'); window.location.href='list.php';</script>";
    exit();
}

$stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
$stmt->bind_param("i", $id);

if ($stmt->execute()) {
    echo "<script>alert('تم حذف المستخدم بنجاح'); window.location.href='list.php';</script>";
    exit();
} else {
    echo "<script>alert('حدث خطأ أثناء الحذف'); window.location.href='list.php';</script>";
    exit();
}
?>