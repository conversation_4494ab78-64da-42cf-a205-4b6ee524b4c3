<?php
session_start();
include '../../includes/auth_admin.php';
include '../../includes/header.php';
renderHeader("تعديل مستخدم", false, "list.php");
?>
<div class="d-flex justify-content-end mb-3" style="max-width:600px;margin:0 auto;">
    <a href="/rasseem/admin/users/list.php" class="btn btn-outline-info btn-lg" style="border-radius:30px;">
        <span style="font-size:1.2em;">⬅️</span> الرجوع لقائمة المستخدمين
    </a>
    <a href="/rasseem/admin/index.php" class="btn btn-outline-primary btn-lg ms-2" style="border-radius:30px;">
        <span style="font-size:1.2em;">🏠</span> الصفحة الرئيسية
    </a>
</div>
<?php
include '../../config/db.php';

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    echo "<div class='alert alert-danger'>معرّف المستخدم غير صالح.</div>";
    include '../../includes/footer.php';
    renderFooter();
    exit();
}

$id = intval($_GET['id']);
$stmt = $conn->prepare("SELECT id, ar_name, username, role FROM users WHERE id = ?");
$stmt->bind_param("i", $id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows !== 1) {
    echo "<div class='alert alert-danger'>المستخدم غير موجود.</div>";
    include '../../includes/footer.php';
    renderFooter();
    exit();
}

$user = $result->fetch_assoc();
?>
<style>
    .edit-user-card {
        max-width: 500px;
        margin: 40px auto 0 auto;
        background: #23272b;
        border-radius: 18px;
        box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.25);
        padding: 35px 30px 30px 30px;
        color: #fff;
        position: relative;
        overflow: hidden;
    }
    .edit-user-card:before {
        content: "";
        position: absolute;
        top: -60px;
        right: -60px;
        width: 180px;
        height: 180px;
        background: linear-gradient(135deg, #1DA1F2 0%, #0d6efd 100%);
        opacity: 0.13;
        border-radius: 50%;
        z-index: 0;
    }
    .edit-user-card h4 {
        color: #1DA1F2;
        font-weight: bold;
        margin-bottom: 25px;
        z-index: 1;
        position: relative;
    }
    .edit-user-card label {
        color: #b5b5b5;
        font-weight: 500;
    }
    .edit-user-card .form-control,
    .edit-user-card .form-select {
        background: #181a1b;
        color: #fff;
        border: 1px solid #222;
        border-radius: 8px;
    }
    .edit-user-card .form-control:focus,
    .edit-user-card .form-select:focus {
        border-color: #1DA1F2;
        box-shadow: 0 0 0 0.2rem rgba(29,161,242,.25);
    }
    .edit-user-card .btn-primary {
        background: linear-gradient(90deg, #1DA1F2 0%, #0d6efd 100%);
        border: none;
        font-weight: bold;
        border-radius: 30px;
        box-shadow: 0 4px 15px rgba(29, 161, 242, 0.2);
        transition: all 0.3s;
    }
    .edit-user-card .btn-primary:hover {
        background: linear-gradient(90deg, #0d6efd 0%, #1DA1F2 100%);
        transform: scale(1.04);
    }
</style>
<div class="edit-user-card">
    <h4 class="mb-4 text-center">✏️ تعديل بيانات المستخدم</h4>
    <form method="POST" action="edit_handler.php">
        <input type="hidden" name="id" value="<?= $user['id'] ?>">
        <div class="mb-3">
            <label for="ar_name" class="form-label">الاسم بالعربية</label>
            <input type="text" class="form-control" id="ar_name" name="ar_name" required value="<?= htmlspecialchars($user['ar_name']) ?>">
        </div>
        <div class="mb-3">
            <label for="username" class="form-label">اسم المستخدم</label>
            <input type="text" class="form-control" id="username" name="username" required value="<?= htmlspecialchars($user['username']) ?>">
        </div>
        <div class="mb-3">
            <label for="role" class="form-label">الصلاحية</label>
            <select class="form-select" id="role" name="role" required>
                <option value="مدير" <?= $user['role'] == 'مدير' ? 'selected' : '' ?>>مدير</option>
                <option value="إدارة الجوال" <?= $user['role'] == 'إدارة الجوال' ? 'selected' : '' ?>>إدارة الجوال</option>
                <option value="المخابر" <?= $user['role'] == 'المخابر' ? 'selected' : '' ?>>المخابر</option>
                <option value="الإدارة العامة" <?= $user['role'] == 'الإدارة العامة' ? 'selected' : '' ?>>الإدارة العامة</option>
                <option value="الهندسة" <?= $user['role'] == 'الهندسة' ? 'selected' : '' ?>>الهندسة</option>
                <option value="الصيانة" <?= $user['role'] == 'الصيانة' ? 'selected' : '' ?>>الصيانة</option>
            </select>
        </div>
        <div class="mb-3">
            <label for="password" class="form-label">كلمة المرور الجديدة (اختياري)</label>
            <input type="password" class="form-control" id="password" name="password" placeholder="اتركه فارغًا إذا لا تريد التغيير">
        </div>
        <button type="submit" class="btn btn-primary w-100">حفظ التعديلات</button>
    </form>
</div>
<?php
include '../../includes/footer.php';
renderFooter();
?>