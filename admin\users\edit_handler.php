<?php
session_start();
include '../../includes/auth_admin.php';
include '../../config/db.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['id'])) {
    $id = intval($_POST['id']);
    $ar_name = $_POST['ar_name'];
    $username = $_POST['username'];
    $role = $_POST['role'];

    // تحقق من عدم وجود اسم مستخدم مكرر (عدا المستخدم الحالي)
    $stmt = $conn->prepare("SELECT id FROM users WHERE username = ? AND id != ?");
    $stmt->bind_param("si", $username, $id);
    $stmt->execute();
    $stmt->store_result();
    if ($stmt->num_rows > 0) {
        echo "<script>alert('اسم المستخدم مستخدم بالفعل'); window.location.href='edit.php?id=$id';</script>";
        exit();
    }
    $stmt->close();

    // تحديث بيانات المستخدم
    if (!empty($_POST['password'])) {
        $password = password_hash($_POST['password'], PASSWORD_DEFAULT);
        $stmt = $conn->prepare("UPDATE users SET ar_name = ?, username = ?, role = ?, password = ? WHERE id = ?");
        $stmt->bind_param("ssssi", $ar_name, $username, $role, $password, $id);
    } else {
        $stmt = $conn->prepare("UPDATE users SET ar_name = ?, username = ?, role = ? WHERE id = ?");
        $stmt->bind_param("sssi", $ar_name, $username, $role, $id);
    }

    if ($stmt->execute()) {
        echo "<script>alert('تم تحديث بيانات المستخدم بنجاح'); window.location.href='list.php';</script>";
        exit();
    } else {
        echo "<script>alert('حدث خطأ أثناء التحديث'); window.location.href='edit.php?id=$id';</script>";
        exit();
    }
} else {
    header("Location: list.php");
    exit();
}
?>