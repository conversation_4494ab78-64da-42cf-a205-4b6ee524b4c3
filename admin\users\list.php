<?php
session_start();
include '../../includes/auth_admin.php';
include '../../includes/header.php';
renderHeader("قائمة المستخدمين - المدير", false, "index.php");
?>
<div class="d-flex justify-content-end mb-3" style="max-width:600px;margin:0 auto;">
    <a href="/rasseem/admin/index.php" class="btn btn-outline-primary btn-lg" style="border-radius:30px;">
        <span style="font-size:1.2em;">🏠</span> الصفحة الرئيسية
    </a>
</div>
<?php
include '../../config/db.php';
$sql = "SELECT id, username, role, ar_name FROM users ORDER BY role";
$result = $conn->query($sql);
?>
<h3>📋 قائمة المستخدمين</h3>
<a href="add.php" class="btn btn-success btn-sm mb-3">➕ إضافة مستخدم جديد</a>
<table class="table table-striped table-dark mt-3">
    <thead>
        <tr>
            <th>#</th>
            <th>الاسم بالعربية</th>
            <th>اسم المستخدم</th>
            <th>الصلاحية</th>
            <th>تعديل</th>
            <th>حذف</th>
        </tr>
    </thead>
    <tbody>
        <?php if ($result && $result->num_rows > 0): ?>
            <?php while ($row = $result->fetch_assoc()): ?>
                <tr>
                    <td><?= $row['id'] ?></td>
                    <td><?= htmlspecialchars($row['ar_name'] ?? 'غير متوفر') ?></td>
                    <td><?= htmlspecialchars($row['username']) ?></td>
                    <td><?= htmlspecialchars($row['role']) ?></td>
                    <td>
                        <a href="edit.php?id=<?= $row['id'] ?>" class="btn btn-sm btn-warning">تعديل</a>
                    </td>
                    <td>
                        <a href="delete.php?id=<?= $row['id'] ?>" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا المستخدم؟');">حذف</a>
                    </td>
                </tr>
            <?php endwhile; ?>
        <?php else: ?>
            <tr><td colspan="6" class="text-center">لا يوجد مستخدمين</td></tr>
        <?php endif; ?>
    </tbody>
</table>
<?php
include '../../includes/footer.php';
renderFooter();
?>