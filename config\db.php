<?php
// Load environment variables
$env_path = __DIR__ . '/../.env';
if (file_exists($env_path)) {
    $env = parse_ini_file($env_path);
} else {
    die("ملف الإعدادات غير موجود");
}

$host = $env['DB_HOST'] ?? 'localhost';
$user = $env['DB_USER'] ?? 'root';
$pass = $env['DB_PASS'] ?? '';
$db = $env['DB_NAME'] ?? 'rassseem';

$conn = new mysqli($host, $user, $pass, $db);

if ($conn->connect_error) {
    error_log("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    die("حدث خطأ في النظام، يرجى المحاولة لاحقاً");
}
?>