<?php
session_start();
include '../includes/auth.php';
include '../includes/header.php';
renderHeader("الإدارة العامة - الرصين", false, "index.php");
include '../config/db.php';
// جلب إحصائيات التكتات
$total_count = 0;
$open_count = 0;
$closed_count = 0;
$res = $conn->query("SELECT COUNT(*) as total FROM tickets");
if ($res) {
    $row = $res->fetch_assoc();
    $total_count = $row['total'];
}
$res2 = $conn->query("SELECT COUNT(*) as open FROM tickets WHERE status = 'مفتوحة'");
if ($res2) {
    $row2 = $res2->fetch_assoc();
    $open_count = $row2['open'];
}
$res3 = $conn->query("SELECT COUNT(*) as closed FROM tickets WHERE status = 'مغلقة'");
if ($res3) {
    $row3 = $res3->fetch_assoc();
    $closed_count = $row3['closed'];
}
?>
<style>
    body {
        background: linear-gradient(-45deg, #0D1117, #1E2124, #0F1319, #1A1C1F);
        background-size: 400% 400%;
        animation: gradientBG 15s ease infinite;
    }
    @keyframes gradientBG {
        0% {background-position: 0% 50%;}
        50% {background-position: 100% 50%;}
        100% {background-position: 0% 50%;}
    }
    .dashboard-container {
        padding: 30px 0;
    }
    .dashboard-title {
        color: #fff;
        text-shadow: 0 0 10px rgba(29, 161, 242, 0.5);
        margin-bottom: 30px;
    }
    .card-custom {
        background-color: #1E2124;
        border: none;
        border-radius: 15px;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
        height: 100%;
    }
    .card-custom:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(29, 161, 242, 0.4);
    }
    .card-custom h5 {
        color: #1DA1F2;
        font-weight: bold;
    }
    .card-custom p {
        color: #aaa;
    }
    .btn-outline-primary, .btn-custom {
        color: #1DA1F2;
        border-color: #1DA1F2;
        background: none;
        font-weight: bold;
        border-radius: 30px;
        box-shadow: 0 4px 15px rgba(29, 161, 242, 0.2);
        transition: all 0.3s;
    }
    .btn-outline-primary:hover, .btn-custom:hover {
        background-color: #1DA1F2;
        color: white;
    }
    .btn-secondary, .btn-info {
        border-radius: 30px;
        font-weight: bold;
    }
    .logout-btn {
        float: left;
        margin-bottom: 20px;
    }
</style>
<div class="dashboard-container">
    <div class="d-flex justify-content-end mb-3" style="max-width:900px;margin:0 auto;">
        <a href="/rasseem/logout.php" class="btn btn-outline-danger btn-lg" style="border-radius:30px;">
            <span style="font-size:1.2em;">🚪</span> تسجيل الخروج
        </a>
    </div>
    <h1 class="text-center dashboard-title">مرحبًا <?= isset($_SESSION['user']['ar_name']) ? htmlspecialchars($_SESSION['user']['ar_name']) : '' ?> 👋</h1>
    <p class="lead text-center mb-5" style="color: #aaa">لوحة تحكم الإدارة العامة - نظام الرصين</p>
    <div class="row justify-content-center mb-4">
        <div class="col-md-4 mb-2">
            <div class="card text-center" style="background: #1DA1F2; color: #fff; border-radius: 15px;">
                <div class="card-body">
                    <div style="font-size:2em;">📬</div>
                    <div style="font-size:1.2em;">مجموع التكتات</div>
                    <div style="font-size:2em; font-weight:bold;"><?= $total_count ?></div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-2">
            <div class="card text-center" style="background: #ffc107; color: #222; border-radius: 15px;">
                <div class="card-body">
                    <div style="font-size:2em;">⏳</div>
                    <div style="font-size:1.2em;">التكت المفتوح</div>
                    <div style="font-size:2em; font-weight:bold;"><?= $open_count ?></div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-2">
            <div class="card text-center" style="background: #28a745; color: #fff; border-radius: 15px;">
                <div class="card-body">
                    <div style="font-size:2em;">✅</div>
                    <div style="font-size:1.2em;">التكت المغلق</div>
                    <div style="font-size:2em; font-weight:bold;"><?= $closed_count ?></div>
                </div>
            </div>
        </div>
    </div>
    <div class="row g-4">
        <div class="col-md-6 col-lg-4">
            <div class="card card-custom text-center p-4">
                <h5>📩 عرض التذاكر</h5>
                <p class="mb-3">مشاهدة التذاكر الواردة والصادرة</p>
                <a href="/rasseem/dashboard/tickets/all.php" class="btn btn-outline-success">عرض التذاكر</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-4">
            <div class="card card-custom text-center p-4">
                <h5>📁 عرض المواقف</h5>
                <p class="mb-3">مراجعة المواقف اليومية من المخابر</p>
                <a href="#" class="btn btn-outline-warning">عرض المواقف</a>
            </div>
        </div>
    </div>
</div>
<?php
include '../includes/footer.php';
renderFooter();
?>