<?php
session_start();
include '../../includes/auth.php';
include '../../includes/header.php';
renderHeader("تفاصيل التذكرة", false, "../index.php");
include '../../includes/ticket_nav.php';
include '../../config/db.php';

// جلب بيانات التذكرة
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    echo "<div class='alert alert-danger'>معرّف التذكرة غير صالح.</div>";
    include '../../includes/footer.php';
    renderFooter();
    exit();
}
$ticket_id = intval($_GET['id']);
$stmt = $conn->prepare("SELECT * FROM tickets WHERE id = ?");
$stmt->bind_param("i", $ticket_id);
$stmt->execute();
$ticket = $stmt->get_result()->fetch_assoc();
if (!$ticket) {
    echo "<div class='alert alert-danger'>التذكرة غير موجودة.</div>";
    include '../../includes/footer.php';
    renderFooter();
    exit();
}

// معالجة الإغلاق والتوجيه والرد
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // إغلاق التذكرة
    if (isset($_POST['close_ticket'])) {
        $stmt = $conn->prepare("UPDATE tickets SET status = 'مغلقة' WHERE id = ?");
        $stmt->bind_param("i", $ticket_id);
        $stmt->execute();
        header("Location: view.php?id=$ticket_id");
        exit();
    }
    // توجيه التذكرة
    if (isset($_POST['forwarded_to'])) {
        $forwarded_to = $_POST['forwarded_to'];
        $stmt = $conn->prepare("UPDATE tickets SET forwarded_to = ? WHERE id = ?");
        $stmt->bind_param("si", $forwarded_to, $ticket_id);
        $stmt->execute();
        header("Location: view.php?id=$ticket_id");
        exit();
    }
    // إضافة رد جديد
    if (isset($_POST['reply'])) {
        $reply = trim($_POST['reply']);
        $section = $_SESSION['user']['role'];
        $ar_name = isset($_SESSION['user']['ar_name']) ? $_SESSION['user']['ar_name'] : '';
        if ($reply !== '') {
            $stmt = $conn->prepare("INSERT INTO ticket_replies (ticket_id, section, ar_name, reply, created_at) VALUES (?, ?, ?, ?, NOW())");
            $stmt->bind_param("isss", $ticket_id, $section, $ar_name, $reply);
            $stmt->execute();
        }
        header("Location: view.php?id=$ticket_id");
        exit();
    }
}

// جلب الردود
$replies = [];
$res = $conn->prepare("SELECT * FROM ticket_replies WHERE ticket_id = ? ORDER BY created_at ASC");
$res->bind_param("i", $ticket_id);
$res->execute();
$replies_result = $res->get_result();
while ($row = $replies_result->fetch_assoc()) {
    $replies[] = $row;
}
?>
<style>
    .gov-ticket-card {
        max-width: 700px;
        margin: 40px auto 0 auto;
        background: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 24px 0 rgba(0,0,0,0.13);
        padding: 35px 30px 30px 30px;
        color: #222;
        border: 2px solid #0d6efd;
        font-family: "Cairo", "Tajawal", Arial, sans-serif;
        position: relative;
    }
    .gov-ticket-card h3 {
        color: #0d6efd;
        font-weight: bold;
        margin-bottom: 18px;
        text-align: center;
    }
    .gov-ticket-card .ticket-label {
        color: #444;
        font-weight: 600;
        margin-bottom: 2px;
    }
    .gov-ticket-card .ticket-value {
        font-size: 1.1rem;
        margin-bottom: 12px;
        background: #f5f7fa;
        border-radius: 6px;
        padding: 8px 12px;
        border: 1px solid #e3e6ea;
    }
    .gov-ticket-card .ticket-image {
        display: block;
        margin: 18px auto 18px auto;
        max-width: 350px;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(13,110,253,0.13);
        border: 1px solid #e3e6ea;
    }
    .gov-ticket-card .badge {
        font-size: 1rem;
        padding: 6px 16px;
        border-radius: 20px;
    }
    .gov-ticket-card .gov-section-title {
        color: #0d6efd;
        font-size: 1.2em;
        font-weight: bold;
        margin-top: 30px;
        margin-bottom: 15px;
        border-bottom: 2px solid #e3e6ea;
        padding-bottom: 5px;
    }
    .gov-ticket-actions {
        display: flex;
        justify-content: center;
        gap: 15px;
        background: #f5f7fa;
        border-top: 2px solid #e3e6ea;
        border-radius: 0 0 12px 12px;
        padding: 18px 0 8px 0;
        margin: 0 -30px -30px -30px;
    }
    .gov-ticket-actions .btn {
        font-weight: bold;
        border-radius: 30px;
        padding: 8px 24px;
    }
    .gov-ticket-actions .btn-danger {
        background: #dc3545;
        border: none;
    }
    .gov-ticket-actions .btn-danger:hover {
        background: #b52a37;
    }
    .gov-ticket-actions .btn-warning {
        background: #ffc107;
        color: #222;
        border: none;
    }
    .gov-ticket-actions .btn-warning:hover {
        background: #e0a800;
        color: #fff;
    }
</style>
<div class="gov-ticket-card">
    <h3>تفاصيل التذكرة رقم <?= $ticket['id'] ?></h3>
    <?php if (!empty($ticket['image'])): ?>
        <img src="../../assets/images/tickets/<?= htmlspecialchars($ticket['image']) ?>" alt="صورة التذكرة" class="ticket-image">
    <?php endif; ?>

    <div class="gov-section-title">الردود على التذكرة</div>
    <?php if (count($replies) > 0): ?>
        <?php foreach ($replies as $reply): ?>
            <div class="card mb-2" style="background:#f5f7fa;border-radius:8px;">
                <div class="card-body">
                    <b style="color:#0d6efd"><?= htmlspecialchars($reply['ar_name']) ?>:</b>
                    <p class="mb-0" style="color:#222"><?= nl2br(htmlspecialchars($reply['reply'])) ?></p>
                    <small class="text-muted"><?= $reply['created_at'] ?></small>
                </div>
            </div>
        <?php endforeach; ?>
    <?php else: ?>
        <div class="alert alert-info">لا توجد ردود بعد.</div>
    <?php endif; ?>
    <form method="POST" class="mt-4">
        <div class="mb-3">
            <label for="reply" class="form-label">إضافة رد جديد</label>
            <textarea class="form-control bg-light text-dark" id="reply" name="reply" required></textarea>
        </div>
        <button type="submit" class="btn btn-primary">إرسال الرد</button>
    </form>
    <div class="gov-section-title" style="margin-top:35px;">معلومات التذكرة</div>
    <div class="ticket-label">وصف المشكلة:</div>
    <div class="ticket-value"><?= htmlspecialchars($ticket['description']) ?></div>
    <div class="ticket-label">مضمون التكت:</div>
    <div class="ticket-value"><?= isset($ticket['content']) ? nl2br(htmlspecialchars($ticket['content'])) : '<span class="text-muted">غير متوفر</span>' ?></div>
    <div class="ticket-label">نوع التكت:</div>
    <div class="ticket-value">
        <?php if ($ticket['status'] == 'مفتوحة'): ?>
            <span class="badge bg-warning text-dark">مفتوحة</span>
        <?php elseif ($ticket['status'] == 'مغلقة'): ?>
            <span class="badge bg-success">مغلقة</span>
        <?php elseif ($ticket['status'] == 'قيد المعالجة'): ?>
            <span class="badge bg-info text-dark">قيد المعالجة</span>
        <?php else: ?>
            <span class="badge bg-secondary"><?= htmlspecialchars($ticket['status']) ?></span>
        <?php endif; ?>
    </div>
    <div class="ticket-label">القسم الموجه إليه:</div>
    <div class="ticket-value">
        <?= $ticket['forwarded_to'] ? htmlspecialchars($ticket['forwarded_to']) : '<span class="text-muted">غير موجهة</span>' ?>
    </div>
    <div class="ticket-label">المصدر:</div>
    <div class="ticket-value"><?= htmlspecialchars($ticket['source']) ?></div>

    <div class="gov-ticket-actions mt-4">
        <form method="POST" class="d-inline-block m-0 p-0" style="display:inline;">
            <select name="forwarded_to" class="form-select bg-light text-dark d-inline-block" style="width:auto;display:inline-block;" required>
                <option value="" disabled selected>اختر القسم</option>
                <option value="الإدارة العامة">الإدارة العامة</option>
                <option value="المخابر">المخابر</option>
                <option value="إدارة الجوال">إدارة الجوال</option>
                <option value="الهندسة">الهندسة</option>
                <option value="الصيانة">الصيانة</option>
            </select>
            <button type="submit" class="btn btn-warning ms-2">توجيه</button>
        </form>
        <?php if ($ticket['status'] != 'مغلقة'): ?>
        <form method="POST" class="d-inline-block m-0 p-0" style="display:inline;">
            <button type="submit" name="close_ticket" class="btn btn-danger ms-2">
                <span style="font-size:1.2em;">🔒</span> إغلاق التكت
            </button>
        </form>
        <?php endif; ?>
    </div>
</div>
<?php
include '../../includes/footer.php';
renderFooter();
?>