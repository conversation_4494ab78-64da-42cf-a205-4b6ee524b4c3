<?php
session_start();
include '../includes/auth_engineering.php';
include '../includes/header.php';
renderHeader("قاعدة بيانات الأجهزة - الهندسة", true, "index.php");
include '../config/db.php';

// التحقق من وجود جدول الأجهزة وإنشائه إذا لم يكن موجودًا
$sql_check = "SHOW TABLES LIKE 'devices'";
$result = $conn->query($sql_check);
if ($result->num_rows == 0) {
    $sql_create = "CREATE TABLE devices (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        device_name VARCHAR(255) NOT NULL,
        device_type VARCHAR(100) NOT NULL,
        serial_number VARCHAR(100) NOT NULL,
        location VARCHAR(255) NOT NULL,
        status VARCHAR(50) NOT NULL,
        notes TEXT,
        added_by VARCHAR(100) NOT NULL,
        added_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $conn->query($sql_create);
}

// إضافة جهاز جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_device'])) {
    $device_name = $_POST['device_name'];
    $device_type = $_POST['device_type'];
    $serial_number = $_POST['serial_number'];
    $location = $_POST['location'];
    $status = $_POST['status'];
    $notes = $_POST['notes'];
    $added_by = $_SESSION['user']['username'];
    
    $sql = "INSERT INTO devices (device_name, device_type, serial_number, location, status, notes, added_by) 
            VALUES (?, ?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("sssssss", $device_name, $device_type, $serial_number, $location, $status, $notes, $added_by);
    
    if ($stmt->execute()) {
        echo "<script>alert('تمت إضافة الجهاز بنجاح');</script>";
    } else {
        echo "<script>alert('حدث خطأ أثناء إضافة الجهاز');</script>";
    }
}

// جلب قائمة الأجهزة
$devices = [];
$sql = "SELECT * FROM devices ORDER BY id DESC";
$result = $conn->query($sql);
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $devices[] = $row;
    }
}
?>

<div class="container py-4">
    <h2 class="mb-4">💾 قاعدة بيانات الأجهزة</h2>
    
    <button type="button" class="btn btn-primary mb-4" data-bs-toggle="modal" data-bs-target="#addDeviceModal">
        ➕ إضافة جهاز جديد
    </button>
    
    <div class="table-responsive">
        <table class="table table-striped table-dark">
            <thead>
                <tr>
                    <th>#</th>
                    <th>اسم الجهاز</th>
                    <th>النوع</th>
                    <th>الرقم التسلسلي</th>
                    <th>الموقع</th>
                    <th>الحالة</th>
                    <th>ملاحظات</th>
                    <th>تاريخ الإضافة</th>
                </tr>
            </thead>
            <tbody>
                <?php if (count($devices) > 0): ?>
                    <?php foreach ($devices as $device): ?>
                        <tr>
                            <td><?= $device['id'] ?></td>
                            <td><?= htmlspecialchars($device['device_name']) ?></td>
                            <td><?= htmlspecialchars($device['device_type']) ?></td>
                            <td><?= htmlspecialchars($device['serial_number']) ?></td>
                            <td><?= htmlspecialchars($device['location']) ?></td>
                            <td>
                                <?php if ($device['status'] == 'يعمل'): ?>
                                    <span class="badge bg-success">يعمل</span>
                                <?php elseif ($device['status'] == 'معطل'): ?>
                                    <span class="badge bg-danger">معطل</span>
                                <?php else: ?>
                                    <span class="badge bg-warning text-dark">قيد الصيانة</span>
                                <?php endif; ?>
                            </td>
                            <td><?= htmlspecialchars($device['notes']) ?></td>
                            <td><?= $device['added_date'] ?></td>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="8" class="text-center">لا توجد أجهزة مسجلة</td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- Modal إضافة جهاز جديد -->
<div class="modal fade" id="addDeviceModal" tabindex="-1" aria-labelledby="addDeviceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content bg-dark text-white">
            <div class="modal-header">
                <h5 class="modal-title" id="addDeviceModalLabel">إضافة جهاز جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="device_name" class="form-label">اسم الجهاز</label>
                        <input type="text" class="form-control bg-dark text-white" id="device_name" name="device_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="device_type" class="form-label">نوع الجهاز</label>
                        <select class="form-select bg-dark text-white" id="device_type" name="device_type" required>
                            <option value="جهاز قياس">جهاز قياس</option>
                            <option value="جهاز إرسال">جهاز إرسال</option>
                            <option value="جهاز استقبال">جهاز استقبال</option>
                            <option value="هوائي">هوائي</option>
                            <option value="محول">محول</option>
                            <option value="أخرى">أخرى</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="serial_number" class="form-label">الرقم التسلسلي</label>
                        <input type="text" class="form-control bg-dark text-white" id="serial_number" name="serial_number" required>
                    </div>
                    <div class="mb-3">
                        <label for="location" class="form-label">الموقع</label>
                        <input type="text" class="form-control bg-dark text-white" id="location" name="location" required>
                    </div>
                    <div class="mb-3">
                        <label for="status" class="form-label">الحالة</label>
                        <select class="form-select bg-dark text-white" id="status" name="status" required>
                            <option value="يعمل">يعمل</option>
                            <option value="معطل">معطل</option>
                            <option value="قيد الصيانة">قيد الصيانة</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control bg-dark text-white" id="notes" name="notes" rows="3"></textarea>
                    </div>
                    <input type="hidden" name="add_device" value="1">
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
include '../includes/footer.php';
renderFooter();
?>