<?php
session_start();
include '../includes/auth_engineering.php';
include '../includes/header.php';
renderHeader("الهندسة - الرصين", false, "index.php");
include '../config/db.php';

// جلب إحصائيات التكتات
$total_count = 0;
$open_count = 0;
$closed_count = 0;
$res = $conn->query("SELECT COUNT(*) as total FROM tickets WHERE forwarded_to = 'الهندسة'");
if ($res) {
    $row = $res->fetch_assoc();
    $total_count = $row['total'];
}
$res2 = $conn->query("SELECT COUNT(*) as open FROM tickets WHERE forwarded_to = 'الهندسة' AND status = 'مفتوحة'");
if ($res2) {
    $row2 = $res2->fetch_assoc();
    $open_count = $row2['open'];
}
$res3 = $conn->query("SELECT COUNT(*) as closed FROM tickets WHERE forwarded_to = 'الهندسة' AND status = 'مغلقة'");
if ($res3) {
    $row3 = $res3->fetch_assoc();
    $closed_count = $row3['closed'];
}
?>

<!-- تضمين ملف CSS المخصص -->
<link rel="stylesheet" href="engineering-style.css">

<!-- إضافة أنماط CSS مباشرة في الصفحة -->
<style>
    body {
        background-color: #0D1117 !important;
        color: #E5E5E5 !important;
    }
</style>

<div class="dashboard-container">
    <div class="d-flex justify-content-end mb-3" style="max-width:900px;margin:0 auto;">
        <a href="/rasseem/logout.php" class="btn btn-outline-danger btn-lg" style="border-radius:30px;">
            <span style="font-size:1.2em;">🚪</span> تسجيل الخروج
        </a>
    </div>
    <h1 class="text-center dashboard-title">مرحبًا <?= isset($_SESSION['user']['ar_name']) ? htmlspecialchars($_SESSION['user']['ar_name']) : '' ?> 👋</h1>
    <p class="lead text-center mb-5" style="color: #aaa">لوحة تحكم قسم الهندسة - نظام الرصين</p>
    
    <div class="row justify-content-center mb-4">
        <div class="col-md-4 mb-2">
            <div class="card text-center" style="background: #1DA1F2; color: #fff; border-radius: 15px;">
                <div class="card-body">
                    <div style="font-size:2em;">📬</div>
                    <h5>إجمالي التذاكر</h5>
                    <div style="font-size:2em; font-weight:bold;"><?= $total_count ?></div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-2">
            <div class="card text-center" style="background: #28a745; color: #fff; border-radius: 15px;">
                <div class="card-body">
                    <div style="font-size:2em;">📩</div>
                    <h5>التذاكر المفتوحة</h5>
                    <div style="font-size:2em; font-weight:bold;"><?= $open_count ?></div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-2">
            <div class="card text-center" style="background: #dc3545; color: #fff; border-radius: 15px;">
                <div class="card-body">
                    <div style="font-size:2em;">📦</div>
                    <h5>التذاكر المغلقة</h5>
                    <div style="font-size:2em; font-weight:bold;"><?= $closed_count ?></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row g-4">
        <div class="col-md-6 col-lg-4">
            <div class="card card-custom text-center p-4">
                <h5>📩 عرض التذاكر</h5>
                <p class="mb-3" style="color: #FFFFFF !important;">مشاهدة التذاكر الواردة والصادرة</p>
                <a href="/rasseem/engineering/tickets/all.php" class="btn btn-outline-success">عرض التذاكر</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-4">
            <div class="card card-custom text-center p-4">
                <h5>💾 قاعدة بيانات الأجهزة</h5>
                <p class="mb-3" style="color: #FFFFFF !important;">إدارة قاعدة بيانات الأجهزة</p>
                <a href="/rasseem/engineering/devices_database.php" class="btn btn-outline-primary">عرض الأجهزة</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-4">
            <div class="card card-custom text-center p-4">
                <h5>🌐 إدارة الشبكات</h5>
                <p class="mb-3" style="color: #FFFFFF !important;">إدارة شبكات الطيف والرصد والمدة</p>
                <a href="/rasseem/engineering/networks/spectrum.php" class="btn btn-outline-warning">عرض الشبكات</a>
            </div>
        </div>
    </div>
</div>

<?php
include '../includes/footer.php';
renderFooter();
?>


