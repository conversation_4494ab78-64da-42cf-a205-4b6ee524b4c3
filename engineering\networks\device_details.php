<?php
session_start();
include '../../includes/auth_engineering.php';
include '../../includes/header.php';
include 'mikrotik_functions.php';
renderHeader("تفاصيل جهاز مايكروتك - الهندسة", true, "spectrum_direct.php");

// التحقق من وجود معرف الجهاز
if (!isset($_GET['id'])) {
    echo "<div class='alert alert-danger'>لم يتم تحديد الجهاز!</div>";
    exit;
}

$device_id = $_GET['id'];
$device = null;
$system_info = null;
$interfaces = null;
$error_message = '';
$success_message = '';

// التحقق من وجود ملف تخزين مؤقت للأجهزة
$cache_file = '../../cache/mikrotik_devices.json';
if (file_exists($cache_file)) {
    $devices = json_decode(file_get_contents($cache_file), true);
    
    // البحث عن الجهاز
    foreach ($devices as $d) {
        if ($d['id'] === $device_id) {
            $device = $d;
            break;
        }
    }
}

// إذا لم يتم العثور على الجهاز
if (!$device) {
    echo "<div class='alert alert-danger'>لم يتم العثور على الجهاز!</div>";
    exit;
}

// تحديث معلومات الجهاز
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'refresh') {
    $api = new RouterosAPI();
    $api->port = $device['port'];
    $api->timeout = 10;
    
    if ($api->connect($device['ip'], $device['username'], $device['password'])) {
        // جلب معلومات النظام
        $system_info = getMikrotikSystemInfo($device['ip'], $device['username'], $device['password'], $device['port']);
        
        // جلب معلومات الواجهات
        $interfaces = getMikrotikInterfaces($device['ip'], $device['username'], $device['password'], $device['port']);
        
        $api->disconnect();
        
        // تحديث حالة الجهاز في المصفوفة
        foreach ($devices as &$d) {
            if ($d['id'] === $device_id) {
                $d['status'] = 'متصل';
                $d['uptime'] = $system_info['uptime'];
                $d['cpu'] = $system_info['cpu_load'] . '%';
                $d['memory'] = $system_info['memory_used_percent'] . '%';
                $d['last_check'] = date('Y-m-d H:i:s');
                $d['identity'] = $system_info['identity'];
                break;
            }
        }
        
        // حفظ المصفوفة في ملف التخزين المؤقت
        file_put_contents($cache_file, json_encode($devices));
        
        $success_message = "تم تحديث معلومات الجهاز بنجاح.";
    } else {
        // تحديث حالة الجهاز في المصفوفة
        foreach ($devices as &$d) {
            if ($d['id'] === $device_id) {
                $d['status'] = 'غير متصل';
                $d['last_check'] = date('Y-m-d H:i:s');
                break;
            }
        }
        
        // حفظ المصفوفة في ملف التخزين المؤقت
        file_put_contents($cache_file, json_encode($devices));
        
        $error_message = "فشل الاتصال بالجهاز. تأكد من صحة بيانات الاتصال وأن الجهاز متصل بالشبكة.";
    }
    
    // إعادة تحميل الجهاز بعد التحديث
    foreach ($devices as $d) {
        if ($d['id'] === $device_id) {
            $device = $d;
            break;
        }
    }
}

// جلب معلومات النظام والواجهات إذا كان الجهاز متصل
if ($device['status'] === 'متصل' && !$system_info) {
    $api = new RouterosAPI();
    $api->port = $device['port'];
    $api->timeout = 10;
    
    if ($api->connect($device['ip'], $device['username'], $device['password'])) {
        // جلب معلومات النظام
        $system_info = getMikrotikSystemInfo($device['ip'], $device['username'], $device['password'], $device['port']);
        
        // جلب معلومات الواجهات
        $interfaces = getMikrotikInterfaces($device['ip'], $device['username'], $device['password'], $device['port']);
        
        $api->disconnect();
    }
}
?>

<div class="container mt-4">
    <?php if ($error_message): ?>
        <div class="alert alert-danger">
            <?= $error_message ?>
        </div>
    <?php endif; ?>
    
    <?php if ($success_message): ?>
        <div class="alert alert-success">
            <?= $success_message ?>
        </div>
    <?php endif; ?>
    
    <div class="row mb-4">
        <div class="col-md-6">
            <h4><?= htmlspecialchars($device['name']) ?> - تفاصيل الجهاز</h4>
        </div>
        <div class="col-md-6 text-end">
            <form method="POST">
                <input type="hidden" name="action" value="refresh">
                <button type="submit" class="btn btn-success">تحديث المعلومات</button>
            </form>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card bg-dark text-white mb-4">
                <div class="card-header">
                    <h5>معلومات الجهاز الأساسية</h5>
                </div>
                <div class="card-body">
                    <table class="table table-dark">
                        <tr>
                            <th>الاسم:</th>
                            <td><?= htmlspecialchars($device['name']) ?></td>
                        </tr>
                        <tr>
                            <th>عنوان IP:</th>
                            <td><?= htmlspecialchars($device['ip']) ?></td>
                        </tr>
                        <tr>
                            <th>اسم الجهاز:</th>
                            <td><?= htmlspecialchars($device['identity'] ?? 'غير معروف') ?></td>
                        </tr>
                        <tr>
                            <th>الحالة:</th>
                            <td>
                                <?php if ($device['status'] === 'متصل'): ?>
                                    <span class="badge bg-success">متصل</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">غير متصل</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th>آخر فحص:</th>
                            <td><?= htmlspecialchars($device['last_check']) ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        
        <?php if ($system_info): ?>
        <div class="col-md-6">
            <div class="card bg-dark text-white mb-4">
                <div class="card-header">
                    <h5>معلومات النظام</h5>
                </div>
                <div class="card-body">
                    <table class="table table-dark">
                        <tr>
                            <th>الموديل:</th>
                            <td><?= htmlspecialchars($system_info['model']) ?></td>
                        </tr>
                        <tr>
                            <th>الرقم التسلسلي:</th>
                            <td><?= htmlspecialchars($system_info['serial_number']) ?></td>
                        </tr>
                        <tr>
                            <th>الإصدار:</th>
                            <td><?= htmlspecialchars($system_info['version']) ?></td>
                        </tr>
                        <tr>
                            <th>وقت التشغيل:</th>
                            <td><?= htmlspecialchars($system_info['uptime']) ?></td>
                        </tr>
                        <tr>
                            <th>استخدام المعالج:</th>
                            <td>
                                <div class="progress bg-secondary">
                                    <div class="progress-bar bg-primary" role="progressbar" style="width: <?= $system_info['cpu_load'] ?>%;" aria-valuenow="<?= $system_info['cpu_load'] ?>" aria-valuemin="0" aria-valuemax="100"><?= $system_info['cpu_load'] ?>%</div>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th>استخدام الذاكرة:</th>
                            <td>
                                <div class="progress bg-secondary">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: <?= $system_info['memory_used_percent'] ?>%;" aria-valuenow="<?= $system_info['memory_used_percent'] ?>" aria-valuemin="0" aria-valuemax="100"><?= $system_info['memory_used_percent'] ?>%</div>
                                </div>
                                <small><?= htmlspecialchars($system_info['free_memory']) ?> حر من <?= htmlspecialchars($system_info['total_memory']) ?></small>
                            </td>
                        </tr>
                        <tr>
                            <th>استخدام القرص:</th>
                            <td>
                                <div class="progress bg-secondary">
                                    <div class="progress-bar bg-warning" role="progressbar" style="width: <?= $system_info['hdd_used_percent'] ?>%;" aria-valuenow="<?= $system_info['hdd_used_percent'] ?>" aria-valuemin="0" aria-valuemax="100"><?= $system_info['hdd_used_percent'] ?>%</div>
                                </div>
                                <small><?= htmlspecialchars($system_info['free_hdd']) ?> حر من <?= htmlspecialchars($system_info['total_hdd']) ?></small>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
    
    <?php if ($interfaces): ?>
    <div class="card bg-dark text-white mb-4">
        <div class="card-header">
            <h5>واجهات الشبكة</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-dark table-striped">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>النوع</th>
                            <th>عنوان MAC</th>
                            <th>عناوين IP</th>
                            <th>الحالة</th>
                            <th>السرعة</th>
                            <th>البيانات المستلمة</th>
                            <th>البيانات المرسلة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($interfaces as $interface): ?>
                            <tr>
                                <td><?= htmlspecialchars($interface['name']) ?></td>
                                <td><?= htmlspecialchars($interface['type']) ?></td>
                                <td><?= htmlspecialchars($interface['mac_address']) ?></td>
                                <td>
                                    <?php if (count($interface['addresses']) > 0): ?>
                                        <?php foreach ($interface['addresses'] as $address): ?>
                                            <div><?= htmlspecialchars($address) ?></div>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <span class="text-muted">لا يوجد</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($interface['running']): ?>
                                        <span class="badge bg-success">نشط</span>
                                    <?php elseif ($interface['disabled']): ?>
                                        <span class="badge bg-secondary">معطل</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">غير نشط</span>
                                    <?php endif; ?>
                                </td>
                                <td><?= htmlspecialchars($interface['speed']) ?></td>
                                <td><?= htmlspecialchars($interface['rx_byte']) ?></td>
                                <td><?= htmlspecialchars($interface['tx_byte']) ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php
include '../../includes/footer.php';
renderFooter();
?>