<?php
session_start();
include '../../includes/auth_engineering.php';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>شبكة المدة - الهندسة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        .network-card {
            background: linear-gradient(145deg, #1E2124, #0D1117);
            border-radius: 20px;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4);
            border: none;
            overflow: hidden;
            position: relative;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .network-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
        }
        
        .network-card::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(90deg, #ffc107, #e0a800, #d39e00, #ffc107);
        }
        
        .card-title {
            color: #ffc107;
            font-size: 2.2rem;
            font-weight: bold;
            text-shadow: 0 0 10px rgba(255, 193, 7, 0.3);
            margin-bottom: 2rem;
        }
        
        .back-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(145deg, #6c757d, #5a6268);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 8px 20px;
            font-size: 1rem;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
            text-decoration: none;
        }
        
        .back-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
            background: linear-gradient(145deg, #5a6268, #495057);
            color: white;
            text-decoration: none;
        }
        
        .duration-card {
            background: rgba(13, 17, 23, 0.7);
            border-radius: 15px;
            border: 1px solid rgba(255, 193, 7, 0.2);
            transition: all 0.3s ease;
        }
        
        .duration-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 193, 7, 0.5);
        }
        
        .progress {
            height: 10px;
            border-radius: 5px;
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        .progress-bar {
            background: linear-gradient(90deg, #ffc107, #e0a800);
        }
        
        .add-btn {
            background: linear-gradient(145deg, #ffc107, #e0a800);
            border: none;
            border-radius: 15px;
            padding: 12px 25px;
            font-weight: bold;
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }
        
        .add-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 20px rgba(0, 0, 0, 0.3);
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="card network-card mb-4">
            <div class="card-body p-5">
                <a href="spectrum.php" class="back-btn">
                    <span>⬅️</span> رجوع
                </a>
                
                <h3 class="card-title text-center">⏱️ شبكة المدة</h3>
                <p class="text-center text-muted mb-5">إدارة مدة الاتصالات والمراقبة الزمنية</p>
                
                <div class="d-flex justify-content-end mb-4">
                    <button class="btn btn-warning add-btn">
                        <i class="fas fa-plus-circle me-2"></i> إضافة مدة جديدة
                    </button>
                </div>
                
                <div class="row g-4">
                    <div class="col-md-6">
                        <div class="card duration-card p-3">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0">مدة الاتصال الرئيسية</h5>
                                <span class="badge bg-warning">نشط</span>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">المدة الإجمالية:</small>
                                <span class="ms-2">24 ساعة</span>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">المدة المستهلكة:</small>
                                <span class="ms-2">16 ساعة</span>
                            </div>
                            <div class="mb-3">
                                <div class="progress">
                                    <div class="progress-bar" role="progressbar" style="width: 67%;" aria-valuenow="67" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <div class="d-flex justify-content-between mt-1">
                                    <small>67% مستهلك</small>
                                    <small>8 ساعات متبقية</small>
                                </div>
                            </div>
                            <div class="d-flex justify-content-end gap-2">
                                <button class="btn btn-sm btn-outline-info">تفاصيل</button>
                                <button class="btn btn-sm btn-outline-warning">تمديد</button>
                                <button class="btn btn-sm btn-outline-danger">إيقاف</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card duration-card p-3">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0">مدة الاتصال الاحتياطية</h5>
                                <span class="badge bg-secondary">غير نشط</span>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">المدة الإجمالية:</small>
                                <span class="ms-2">12 ساعة</span>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">المدة المستهلكة:</small>
                                <span class="ms-2">0 ساعة</span>
                            </div>
                            <div class="mb-3">
                                <div class="progress">
                                    <div class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <div class="d-flex justify-content-between mt-1">
                                    <small>0% مستهلك</small>
                                    <small>12 ساعة متبقية</small>
                                </div>
                            </div>
                            <div class="d-flex justify-content-end gap-2">
                                <button class="btn btn-sm btn-outline-info">تفاصيل</button>
                                <button class="btn btn-sm btn-outline-success">تفعيل</button>
                                <button class="btn btn-sm btn-outline-danger">حذف</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-12">
                        <div class="card duration-card p-3">
                            <h5 class="mb-3">سجل المدد السابقة</h5>
                            <div class="table-responsive">
                                <table class="table table-dark table-hover">
                                    <thead>
                                        <tr>
                                            <th>الاسم</th>
                                            <th>المدة الإجمالية</th>
                                            <th>تاريخ البدء</th>
                                            <th>تاريخ الانتهاء</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>مدة الاتصال الشهرية</td>
                                            <td>30 يوم</td>
                                            <td>01/03/2023</td>
                                            <td>31/03/2023</td>
                                            <td><span class="badge bg-success">مكتمل</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-info">عرض</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>مدة الاتصال الطارئة</td>
                                            <td>48 ساعة</td>
                                            <td>15/02/2023</td>
                                            <td>17/02/2023</td>
                                            <td><span class="badge bg-danger">ملغي</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-info">عرض</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>