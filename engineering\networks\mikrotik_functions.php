<?php
/**
 * وظائف خاصة بالاتصال بأجهزة مايكروتك وجلب المعلومات منها
 */

require_once 'routeros_api.php';

/**
 * اختبار الاتصال بجهاز مايكروتك
 * 
 * @param string $ip عنوان IP للجهاز
 * @param string $username اسم المستخدم
 * @param string $password كلمة المرور
 * @param int $port رقم المنفذ
 * @return bool نجاح الاتصال أم لا
 */
function testMikrotikConnection($ip, $username, $password, $port = 8728) {
    $api = new RouterosAPI();
    $api->debug = true; // تمكين وضع التصحيح
    $api->port = $port;
    $api->timeout = 10; // زيادة مهلة الاتصال
    
    // محاولة الاتصال
    if ($api->connect($ip, $username, $password)) {
        $api->disconnect();
        return true;
    }
    
    // طباعة معلومات الخطأ
    error_log("فشل الاتصال بجهاز مايكروتك: " . $api->error);
    return false;
}

/**
 * جلب معلومات النظام من جهاز مايكروتك
 * 
 * @param string $ip عنوان IP للجهاز
 * @param string $username اسم المستخدم
 * @param string $password كلمة المرور
 * @param int $port رقم المنفذ
 * @return array|bool معلومات النظام أو false في حالة فشل الاتصال
 */
function getMikrotikSystemInfo($ip, $username, $password, $port = 8728) {
    $api = new RouterosAPI();
    $api->debug = true; // تمكين وضع التصحيح
    $api->port = $port;
    $api->timeout = 10; // زيادة مهلة الاتصال
    
    try {
        if ($api->connect($ip, $username, $password)) {
            // جلب معلومات النظام
            $system_info = $api->comm('/system/resource/print');
            
            // جلب معلومات الهوية
            $identity = $api->comm('/system/identity/print');
            
            // جلب معلومات الإصدار
            $routerboard = $api->comm('/system/routerboard/print');
            
            $api->disconnect();
            
            // دمج المعلومات في مصفوفة واحدة
            $result = [
                'identity' => $identity[0]['name'] ?? 'غير معروف',
                'model' => $routerboard[0]['model'] ?? 'غير معروف',
                'serial_number' => $routerboard[0]['serial-number'] ?? 'غير معروف',
                'firmware' => $routerboard[0]['firmware'] ?? 'غير معروف',
                'uptime' => $system_info[0]['uptime'] ?? 'غير معروف',
                'version' => $system_info[0]['version'] ?? 'غير معروف',
                'cpu_load' => $system_info[0]['cpu-load'] ?? '0',
                'free_memory' => formatBytes($system_info[0]['free-memory'] ?? 0),
                'total_memory' => formatBytes($system_info[0]['total-memory'] ?? 0),
                'free_hdd' => formatBytes($system_info[0]['free-hdd-space'] ?? 0),
                'total_hdd' => formatBytes($system_info[0]['total-hdd-space'] ?? 0),
                'cpu_count' => $system_info[0]['cpu-count'] ?? '1',
                'cpu_frequency' => $system_info[0]['cpu-frequency'] ?? 'غير معروف',
                'board_name' => $system_info[0]['board-name'] ?? 'غير معروف',
                'architecture_name' => $system_info[0]['architecture-name'] ?? 'غير معروف',
            ];
            
            // حساب النسب المئوية
            $result['memory_used_percent'] = calculatePercentage(
                ($result['total_memory'] - $result['free_memory']), 
                $result['total_memory']
            );
            
            $result['hdd_used_percent'] = calculatePercentage(
                ($result['total_hdd'] - $result['free_hdd']), 
                $result['total_hdd']
            );
            
            return $result;
        }
    } catch (Exception $e) {
        error_log("خطأ أثناء الاتصال بجهاز مايكروتك: " . $e->getMessage());
    }
    
    return false;
}

/**
 * جلب معلومات واجهات الشبكة من جهاز مايكروتك
 * 
 * @param string $ip عنوان IP للجهاز
 * @param string $username اسم المستخدم
 * @param string $password كلمة المرور
 * @param int $port رقم المنفذ
 * @return array|bool معلومات واجهات الشبكة أو false في حالة فشل الاتصال
 */
function getMikrotikInterfaces($ip, $username, $password, $port = 8728) {
    $api = new RouterosAPI();
    $api->debug = true; // تمكين وضع التصحيح
    $api->port = $port;
    $api->timeout = 10; // زيادة مهلة الاتصال
    
    try {
        if ($api->connect($ip, $username, $password)) {
            // جلب قائمة الواجهات
            $interfaces = $api->comm('/interface/print');
            
            // جلب عناوين IP
            $addresses = $api->comm('/ip/address/print');
            
            $api->disconnect();
            
            // معالجة البيانات
            $result = [];
            foreach ($interfaces as $interface) {
                $name = $interface['name'];
                $ip_addresses = [];
                
                // البحث عن عناوين IP المرتبطة بهذه الواجهة
                foreach ($addresses as $address) {
                    if ($address['interface'] === $name) {
                        $ip_addresses[] = $address['address'];
                    }
                }
                
                $result[] = [
                    'name' => $name,
                    'type' => $interface['type'] ?? 'غير معروف',
                    'mac_address' => $interface['mac-address'] ?? 'غير معروف',
                    'running' => isset($interface['running']) && $interface['running'] === 'true',
                    'disabled' => isset($interface['disabled']) && $interface['disabled'] === 'true',
                    'speed' => $interface['speed'] ?? 'غير معروف',
                    'addresses' => $ip_addresses,
                    'rx_byte' => formatBytes($interface['rx-byte'] ?? 0),
                    'tx_byte' => formatBytes($interface['tx-byte'] ?? 0),
                    'last_link_down' => $interface['last-link-down'] ?? 'غير معروف',
                    'last_link_up' => $interface['last-link-up'] ?? 'غير معروف',
                ];
            }
            
            return $result;
        }
    } catch (Exception $e) {
        error_log("خطأ أثناء جلب معلومات واجهات الشبكة: " . $e->getMessage());
    }
    
    return false;
}

/**
 * تحديث حالة جهاز مايكروتك
 * 
 * @param array $device بيانات الجهاز
 * @return array بيانات الجهاز المحدثة
 */
function updateMikrotikStatus($device) {
    // نسخة من بيانات الجهاز
    $updated_device = $device;
    
    // محاولة الاتصال بالجهاز
    if (testMikrotikConnection($device['ip'], $device['username'], $device['password'], $device['port'])) {
        // جلب معلومات النظام
        $system_info = getMikrotikSystemInfo($device['ip'], $device['username'], $device['password'], $device['port']);
        
        if ($system_info) {
            $updated_device['status'] = 'متصل';
            $updated_device['uptime'] = $system_info['uptime'];
            $updated_device['cpu'] = $system_info['cpu_load'] . '%';
            $updated_device['memory'] = $system_info['memory_used_percent'] . '%';
            $updated_device['last_check'] = date('Y-m-d H:i:s');
            $updated_device['identity'] = $system_info['identity'];
        } else {
            $updated_device['status'] = 'غير متصل';
            $updated_device['last_check'] = date('Y-m-d H:i:s');
        }
    } else {
        $updated_device['status'] = 'غير متصل';
        $updated_device['last_check'] = date('Y-m-d H:i:s');
    }
    
    return $updated_device;
}

/**
 * تنسيق حجم البيانات بالبايت إلى صيغة مقروءة
 * 
 * @param int $bytes حجم البيانات بالبايت
 * @return string الحجم بصيغة مقروءة
 */
function formatBytes($bytes, $precision = 2) {
    if ($bytes <= 0) {
        return '0 B';
    }
    
    $units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
    $base = 1024;
    $exponent = floor(log($bytes) / log($base));
    
    return round($bytes / pow($base, $exponent), $precision) . ' ' . $units[$exponent];
}

/**
 * حساب النسبة المئوية
 * 
 * @param float $part الجزء
 * @param float $total الكل
 * @return int النسبة المئوية
 */
function calculatePercentage($part, $total) {
    if ($total <= 0) {
        return 0;
    }
    
    return round(($part / $total) * 100);
}

/**
 * دالة للتحقق من صحة ملف RouterOS API
 * 
 * @return bool صحة الملف
 */
function checkRouterOSAPI() {
    $api_file = __DIR__ . '/routeros_api.php';
    
    if (!file_exists($api_file)) {
        error_log("ملف RouterOS API غير موجود: " . $api_file);
        return false;
    }
    
    // التحقق من وجود الكلاس
    if (!class_exists('RouterosAPI')) {
        error_log("فئة RouterosAPI غير موجودة في الملف");
        return false;
    }
    
    return true;
}

// التحقق من صحة ملف RouterOS API عند تحميل هذا الملف
if (!checkRouterOSAPI()) {
    die("خطأ: ملف RouterOS API غير موجود أو غير صالح");
}
