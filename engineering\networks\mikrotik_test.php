<?php
session_start();
include '../../includes/auth_engineering.php';
include '../../includes/header.php';
include 'mikrotik_functions.php';
renderHeader("اختبار اتصال مايكروتك - الهندسة", true, "spectrum.php");

$error_message = '';
$success_message = '';
$debug_output = '';

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $ip = $_POST['ip'];
    $username = $_POST['username'];
    $password = $_POST['password'];
    $port = intval($_POST['port']);
    
    // تمكين وضع التصحيح
    ob_start();
    
    // إنشاء كائن API
    $api = new RouterosAPI();
    $api->debug = true;
    $api->port = $port;
    $api->timeout = 10;
    
    // محاولة الاتصال
    $connected = $api->connect($ip, $username, $password);
    
    // الحصول على إخراج التصحيح
    $debug_output = ob_get_clean();
    
    if ($connected) {
        // جلب معلومات النظام
        $system_info = $api->comm('/system/resource/print');
        $identity = $api->comm('/system/identity/print');
        
        // إغلاق الاتصال
        $api->disconnect();
        
        $success_message = "تم الاتصال بنجاح! اسم الجهاز: " . ($identity[0]['name'] ?? 'غير معروف');
    } else {
        $error_message = "فشل الاتصال بالجهاز. تأكد من صحة بيانات الاتصال وأن الجهاز متصل بالشبكة.";
    }
}
?>

<div class="
</augment_code_snippet>