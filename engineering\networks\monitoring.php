<?php
session_start();
include '../../includes/auth_engineering.php';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>شبكة الرصد - الهندسة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        .network-card {
            background: linear-gradient(145deg, #1E2124, #0D1117);
            border-radius: 20px;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4);
            border: none;
            overflow: hidden;
            position: relative;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .network-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
        }
        
        .network-card::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(90deg, #1DA1F2, #0d6efd, #ffc107, #0dcaf0);
        }
        
        .card-title {
            color: #1DA1F2;
            font-size: 2.2rem;
            font-weight: bold;
            text-shadow: 0 0 10px rgba(29, 161, 242, 0.3);
            margin-bottom: 2rem;
        }
        
        .back-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(145deg, #6c757d, #5a6268);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 8px 20px;
            font-size: 1rem;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
            text-decoration: none;
        }
        
        .back-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
            background: linear-gradient(145deg, #5a6268, #495057);
            color: white;
            text-decoration: none;
        }
        
        .device-card {
            background: rgba(13, 17, 23, 0.7);
            border-radius: 15px;
            border: 1px solid rgba(29, 161, 242, 0.2);
            transition: all 0.3s ease;
        }
        
        .device-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
            border-color: rgba(29, 161, 242, 0.5);
        }
        
        .status-badge {
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
        }
        
        .status-online {
            background-color: rgba(25, 135, 84, 0.2);
            color: #28a745;
            border: 1px solid rgba(25, 135, 84, 0.4);
        }
        
        .status-offline {
            background-color: rgba(220, 53, 69, 0.2);
            color: #dc3545;
            border: 1px solid rgba(220, 53, 69, 0.4);
        }
        
        .status-warning {
            background-color: rgba(255, 193, 7, 0.2);
            color: #ffc107;
            border: 1px solid rgba(255, 193, 7, 0.4);
        }
        
        .add-device-btn {
            background: linear-gradient(145deg, #0d6efd, #0a58ca);
            border: none;
            border-radius: 15px;
            padding: 12px 25px;
            font-weight: bold;
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }
        
        .add-device-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 20px rgba(0, 0, 0, 0.3);
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="card network-card mb-4">
            <div class="card-body p-5">
                <a href="spectrum.php" class="back-btn">
                    <span>⬅️</span> رجوع
                </a>
                
                <h3 class="card-title text-center">📡 شبكة الرصد</h3>
                <p class="text-center text-muted mb-5">إدارة أجهزة الرصد والمراقبة</p>
                
                <div class="d-flex justify-content-end mb-4">
                    <button class="btn btn-primary add-device-btn">
                        <i class="fas fa-plus-circle me-2"></i> إضافة جهاز جديد
                    </button>
                </div>
                
                <div class="row g-4">
                    <div class="col-md-6 col-lg-4">
                        <div class="card device-card p-3">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0">جهاز الرصد الرئيسي</h5>
                                <span class="status-badge status-online">متصل</span>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">عنوان IP:</small>
                                <span class="ms-2">*************</span>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">الموقع:</small>
                                <span class="ms-2">المركز الرئيسي</span>
                            </div>
                            <div class="mb-3">
                                <small class="text-muted">آخر فحص:</small>
                                <span class="ms-2">منذ 5 دقائق</span>
                            </div>
                            <div class="d-flex justify-content-end gap-2">
                                <button class="btn btn-sm btn-outline-info">تفاصيل</button>
                                <button class="btn btn-sm btn-outline-warning">تعديل</button>
                                <button class="btn btn-sm btn-outline-danger">حذف</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 col-lg-4">
                        <div class="card device-card p-3">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0">جهاز الرصد الشمالي</h5>
                                <span class="status-badge status-warning">تحذير</span>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">عنوان IP:</small>
                                <span class="ms-2">*************</span>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">الموقع:</small>
                                <span class="ms-2">المنطقة الشمالية</span>
                            </div>
                            <div class="mb-3">
                                <small class="text-muted">آخر فحص:</small>
                                <span class="ms-2">منذ 20 دقيقة</span>
                            </div>
                            <div class="d-flex justify-content-end gap-2">
                                <button class="btn btn-sm btn-outline-info">تفاصيل</button>
                                <button class="btn btn-sm btn-outline-warning">تعديل</button>
                                <button class="btn btn-sm btn-outline-danger">حذف</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 col-lg-4">
                        <div class="card device-card p-3">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0">جهاز الرصد الجنوبي</h5>
                                <span class="status-badge status-offline">غير متصل</span>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">عنوان IP:</small>
                                <span class="ms-2">*************</span>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">الموقع:</small>
                                <span class="ms-2">المنطقة الجنوبية</span>
                            </div>
                            <div class="mb-3">
                                <small class="text-muted">آخر فحص:</small>
                                <span class="ms-2">منذ 3 ساعات</span>
                            </div>
                            <div class="d-flex justify-content-end gap-2">
                                <button class="btn btn-sm btn-outline-info">تفاصيل</button>
                                <button class="btn btn-sm btn-outline-warning">تعديل</button>
                                <button class="btn btn-sm btn-outline-danger">حذف</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
