<?php
session_start();
include '../../includes/auth_engineering.php';

// تحديد الصفحة التي سيتم عرضها
$page = isset($_GET['page']) ? $_GET['page'] : '';

// إذا كانت الصفحة هي spectrum_view، قم بتحميل محتوى شبكة الطيف
if ($page === 'spectrum_view') {
    // هنا يمكنك تضمين محتوى شبكة الطيف مباشرة
    include 'spectrum_view_content.php';
    exit; // توقف عن تنفيذ بقية الكود
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>شبكة الطيف - الهندسة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        .network-card {
            background: linear-gradient(145deg, #1E2124, #0D1117);
            border-radius: 20px;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4);
            border: none;
            overflow: hidden;
            position: relative;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .network-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
        }
        
        .network-card::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(90deg, #1DA1F2, #0d6efd, #ffc107, #0dcaf0);
        }
        
        .card-title {
            color: #1DA1F2;
            font-size: 2.2rem;
            font-weight: bold;
            text-shadow: 0 0 10px rgba(29, 161, 242, 0.3);
            margin-bottom: 2rem;
        }
        
        .network-btn {
            border-radius: 15px;
            font-weight: bold;
            padding: 25px;
            font-size: 1.2rem;
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: none;
            position: relative;
            overflow: hidden;
            z-index: 1;
        }
        
        .network-btn:hover {
            transform: translateY(-8px) scale(1.03);
            box-shadow: 0 15px 25px rgba(0, 0, 0, 0.3);
        }
        
        .btn-primary {
            background: linear-gradient(145deg, #0d6efd, #0a58ca);
        }
        
        .btn-warning {
            background: linear-gradient(145deg, #ffc107, #e0a800);
        }
        
        .btn-info {
            background: linear-gradient(145deg, #0dcaf0, #0aa2c0);
        }
        
        .network-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            display: block;
            transition: transform 0.3s ease;
        }
        
        .network-btn:hover .network-icon {
            transform: scale(1.2);
        }
        
        .network-subtitle {
            color: #adb5bd;
            text-align: center;
            margin-bottom: 2rem;
            font-size: 1.1rem;
        }
        
        .back-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(145deg, #6c757d, #5a6268);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 8px 20px;
            font-size: 1rem;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
            text-decoration: none;
        }
        
        .back-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
            background: linear-gradient(145deg, #5a6268, #495057);
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="card network-card mb-4">
            <div class="card-body p-5">
                <a href="../index.php" class="back-btn">
                    <span>⬅️</span> رجوع
                </a>
                
                <h3 class="card-title text-center">🌐 إدارة الشبكات</h3>
                <p class="network-subtitle">اختر الشبكة التي تريد الدخول إليها</p>
                
                <div class="row justify-content-center g-4 mt-4">
                    <div class="col-md-4">
                        <a href="monitoring.php" class="btn btn-primary w-100 py-4 network-btn">
                            <span class="network-icon">📡</span>
                            <span class="network-btn-text">الدخول إلى شبكة الرصد</span>
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="duration.php" class="btn btn-warning w-100 py-4 network-btn">
                            <span class="network-icon">⏱️</span>
                            <span class="network-btn-text">الدخول إلى شبكة المدة</span>
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="spectrum_direct.php" class="btn btn-info w-100 py-4 network-btn">
                            <span class="network-icon">📊</span>
                            <span class="network-btn-text">الدخول إلى شبكة الطيف</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>





















