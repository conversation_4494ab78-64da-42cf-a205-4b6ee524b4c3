<?php
session_start();
include '../../includes/auth_engineering.php';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تفاصيل شبكة الطيف - الهندسة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        .network-card {
            background: linear-gradient(145deg, #1E2124, #0D1117);
            border-radius: 20px;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4);
            border: none;
            overflow: hidden;
            position: relative;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .network-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
        }
        
        .network-card::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(90deg, #0dcaf0, #0aa2c0, #0dcaf0, #0aa2c0);
        }
        
        .card-title {
            color: #0dcaf0;
            font-size: 2.2rem;
            font-weight: bold;
            text-shadow: 0 0 10px rgba(13, 202, 240, 0.3);
            margin-bottom: 2rem;
        }
        
        .back-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(145deg, #6c757d, #5a6268);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 8px 20px;
            font-size: 1rem;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
            text-decoration: none;
        }
        
        .back-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
            background: linear-gradient(145deg, #5a6268, #495057);
            color: white;
            text-decoration: none;
        }
        
        .spectrum-card {
            background: rgba(13, 17, 23, 0.7);
            border-radius: 15px;
            border: 1px solid rgba(13, 202, 240, 0.2);
            transition: all 0.3s ease;
        }
        
        .spectrum-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
            border-color: rgba(13, 202, 240, 0.5);
        }
        
        .frequency-badge {
            background-color: rgba(13, 202, 240, 0.2);
            color: #0dcaf0;
            border: 1px solid rgba(13, 202, 240, 0.4);
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.9rem;
        }
        
        .add-btn {
            background: linear-gradient(145deg, #0dcaf0, #0aa2c0);
            border: none;
            border-radius: 15px;
            padding: 12px 25px;
            font-weight: bold;
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }
        
        .add-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 20px rgba(0, 0, 0, 0.3);
        }
        
        .chart-container {
            height: 300px;
            position: relative;
        }
        
        .chart-placeholder {
            width: 100%;
            height: 100%;
            background: linear-gradient(145deg, rgba(13, 202, 240, 0.1), rgba(13, 202, 240, 0.05));
            border-radius: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: rgba(13, 202, 240, 0.7);
            font-size: 1.5rem;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="card network-card mb-4">
            <div class="card-body p-5">
                <a href="spectrum.php" class="back-btn">
                    <span>⬅️</span> رجوع
                </a>
                
                <h3 class="card-title text-center">📊 شبكة الطيف</h3>
                <p class="text-center text-muted mb-5">تحليل وإدارة الترددات والطيف الكهرومغناطيسي</p>
                
                <div class="d-flex justify-content-end mb-4">
                    <button class="btn btn-info add-btn">
                        <i class="fas fa-plus-circle me-2"></i> إضافة تردد جديد
                    </button>
                </div>
                
                <div class="row g-4">
                    <div class="col-12">
                        <div class="card spectrum-card p-3">
                            <h5 class="mb-3">تحليل الطيف الحالي</h5>
                            <div class="chart-container mb-3">
                                <div class="chart-placeholder">
                                    رسم بياني لتحليل الطيف الترددي
                                </div>
                            </div>
                            <div class="d-flex flex-wrap gap-2 mb-3">
                                <span class="frequency-badge">FM 88-108 MHz</span>
                                <span class="frequency-badge">VHF 174-216 MHz</span>
                                <span class="frequency-badge">UHF 470-806 MHz</span>
                                <span class="frequency-badge">GSM 900 MHz</span>
                                <span class="frequency-badge">GSM 1800 MHz</span>
                                <span class="frequency-badge">3G 2100 MHz</span>
                                <span class="frequency-badge">4G 2600 MHz</span>
                                <span class="frequency-badge">5G 3500 MHz</span>
                            </div>
                            <div class="d-flex justify-content-end">
                                <button class="btn btn-outline-info">تحديث التحليل</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card spectrum-card p-3">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0">الترددات النشطة</h5>
                                <span class="badge bg-info">15 تردد</span>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-dark table-hover">
                                    <thead>
                                        <tr>
                                            <th>التردد</th>
                                            <th>النطاق</th>
                                            <th>القوة</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>98.1 MHz</td>
                                            <td>FM</td>
                                            <td>-45 dBm</td>
                                