<?php
session_start();
require_once '../../includes/routeros_api.class.php';

$cache_file = '../../cache/mikrotik_devices.json';
$devices = file_exists($cache_file) ? json_decode(file_get_contents($cache_file), true) : [];

// معالجة إضافة جهاز جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_device'])) {
    $API = new RouterosAPI();

    // القيم الثابتة المطلوبة
    $ip = '*************';
    $username = '1';
    $password = '1';
    $port = 8728;

    $status = 'غير متصل';
    $uptime = '-';
    $cpu = '-';
    $memory = '-';
    $last_check = date('Y-m-d H:i:s');

    // اختبار الاتصال إذا تم تحديده (افتراضياً مفعل)
    if (isset($_POST['test_connection'])) {
        if ($API->connect($ip, $username, $password, (int)$port)) {
            $status = 'متصل';
            $data = $API->comm('/system/resource/print');
            if (!empty($data[0])) {
                $uptime = $data[0]['uptime'];
                $cpu = $data[0]['cpu-load'] . '%';
                $memory = round((($data[0]['total-memory'] - $data[0]['free-memory']) / $data[0]['total-memory']) * 100) . '%';
            }
            $API->disconnect();
        }
    }

    $new_device = [
        'ip' => $ip,
        'name' => $_POST['device_name'], // فقط اسم الجهاز يدخله المستخدم
        'username' => $username,
        'password' => $password,
        'port' => $port,
        'status' => $status,
        'uptime' => $uptime,
        'cpu' => $cpu,
        'memory' => $memory,
        'last_check' => $last_check,
        'added_date' => date('Y-m-d H:i:s')
    ];

    $devices[] = $new_device;
    if (!is_dir('../../cache')) mkdir('../../cache', 0777, true);
    file_put_contents($cache_file, json_encode($devices));
    header("Location: spectrum_direct.php?added=1");
    exit;
}

// عرض النتيجة
?>
<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <title>مراقبة أجهزة MikroTik</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-dark text-light">
<div class="container py-4">
    <h2 class="mb-4 text-info"><i class="fas fa-network-wired"></i> مراقبة أجهزة MikroTik</h2>

    <table class="table table-bordered table-dark table-hover">
        <thead>
            <tr>
                <th>#</th>
                <th>IP</th>
                <th>الاسم</th>
                <th>الحالة</th>
                <th>uptime</th>
                <th>CPU</th>
                <th>RAM</th>
                <th>تاريخ الإضافة</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($devices as $i => $d): ?>
                <tr>
                    <td><?= $i + 1 ?></td>
                    <td><?= $d['ip'] ?>:<?= $d['port'] ?></td>
                    <td><?= htmlspecialchars($d['name']) ?></td>
                    <td class="<?= $d['status'] == 'متصل' ? 'text-success' : 'text-danger' ?>"><?= $d['status'] ?></td>
                    <td><?= $d['uptime'] ?></td>
                    <td><?= $d['cpu'] ?></td>
                    <td><?= $d['memory'] ?></td>
                    <td><?= $d['added_date'] ?></td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>

    <hr class="border-info">
    <h4 class="text-info">إضافة جهاز جديد</h4>
    <form method="post" class="row g-3">
        <input type="hidden" name="add_device" value="1">

        <!-- فقط اسم الجهاز يدخله المستخدم -->
        <div class="col-md-6">
            <label class="form-label">اسم الجهاز</label>
            <input type="text" name="device_name" class="form-control" required>
        </div>

        <!-- معلومات الاتصال مخفية وثابتة -->
        <input type="hidden" name="device_ip" value="*************">
        <input type="hidden" name="device_username" value="1">
        <input type="hidden" name="device_password" value="1">
        <input type="hidden" name="device_port" value="8728">

        <div class="col-md-6">
            <label class="form-check-label mt-4">
                <input class="form-check-input" type="checkbox" name="test_connection" checked> اختبار الاتصال (مفعل افتراضياً)
            </label>
        </div>

        <div class="col-12">
            <button type="submit" class="btn btn-primary"><i class="fas fa-plus"></i> إضافة</button>
        </div>
    </form>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
