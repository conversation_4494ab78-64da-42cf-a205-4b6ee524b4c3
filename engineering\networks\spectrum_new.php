<?php
session_start();
include '../../includes/auth_engineering.php';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>شبكة الطيف الجديدة - الهندسة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        body {
            background: linear-gradient(-45deg, #0D1117, #1E2124, #0F1319, #1A1C1F);
            background-size: 400% 400%;
            animation: gradientBG 15s ease infinite;
            color: #E5E5E5;
        }
        
        @keyframes gradientBG {
            0% {background-position: 0% 50%;}
            50% {background-position: 100% 50%;}
            100% {background-position: 0% 50%;}
        }
        
        .network-card {
            background-color: #1E2124;
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }
        
        .network-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(13, 202, 240, 0.4);
        }
        
        .card-title {
            color: #0dcaf0;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>شبكة الطيف الجديدة 📊</h2>
            <a href="spectrum.php" class="btn btn-outline-info">
                <span>⬅️</span> رجوع
            </a>
        </div>
        
        <div class="card network-card">
            <div class="card-body">
                <h5 class="card-title">صفحة اختبار</h5>
                <p>هذه صفحة اختبار للتأكد من أن الروابط تعمل بشكل صحيح.</p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>