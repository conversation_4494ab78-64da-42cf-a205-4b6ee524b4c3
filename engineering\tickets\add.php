<?php
session_start();
include '../../includes/auth_engineering.php';
include '../../includes/header.php';
renderHeader("إضافة تذكرة جديدة - الهندسة", false, "../index.php");
include '../../includes/ticket_nav.php';
?>

<style>
    body {
        background: linear-gradient(-45deg, #0D1117, #1E2124, #0F1319, #1A1C1F);
        background-size: 400% 400%;
        animation: gradientBG 15s ease infinite;
    }
    @keyframes gradientBG {
        0% {background-position: 0% 50%;}
        50% {background-position: 100% 50%;}
        100% {background-position: 0% 50%;}
    }
    .ticket-card {
        max-width: 500px;
        margin: 50px auto 0 auto;
        background: #23272b;
        border-radius: 18px;
        box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        padding: 35px 30px 30px 30px;
        color: #fff;
        position: relative;
        overflow: hidden;
    }
    .icon {
        position: absolute;
        top: 10px;
        right: 15px;
        font-size: 24px;
        opacity: 0.5;
    }
</style>

<div class="ticket-card">
    <div class="icon">➕</div>
    <h2 class="text-center mb-4">إضافة تذكرة جديدة</h2>
    <form method="POST" action="../../admin/tickets/add_handler.php" enctype="multipart/form-data">
        <div class="mb-3">
            <label for="image" class="form-label">إرفاق صورة (اختياري)</label>
            <input type="file" class="form-control" id="image" name="image" accept="image/*">
        </div>
        <div class="mb-3">
            <label for="description" class="form-label">وصف المشكلة</label>
            <textarea class="form-control" id="description" name="description" required rows="2" placeholder="اكتب وصف مختصر للمشكلة..."></textarea>
        </div>
        <div class="mb-3">
            <label for="content" class="form-label">مضمون التكت</label>
            <textarea class="form-control" id="content" name="content" required rows="4" placeholder="اكتب تفاصيل التذكرة بشكل كامل..."></textarea>
        </div>
        <div class="mb-3">
            <label for="status" class="form-label">نوع التكت</label>
            <select class="form-select" id="status" name="status" required>
                <option value="مفتوحة">مفتوحة</option>
                <option value="مغلقة">مغلقة</option>
            </select>
        </div>
        <div class="mb-3">
            <label for="forwarded_to" class="form-label">توجيه التذكرة إلى قسم</label>
            <select class="form-select" id="forwarded_to" name="forwarded_to">
                <option value="" selected>غير موجهة</option>
                <option value="الإدارة العامة">الإدارة العامة</option>
                <option value="المخابر">المخابر</option>
                <option value="إدارة الجوال">إدارة الجوال</option>
                <option value="الهندسة">الهندسة</option>
                <option value="الصيانة">الصيانة</option>
            </select>
        </div>
        <input type="hidden" id="source" name="source" value="الهندسة">
        <button type="submit" class="btn btn-success w-100">حفظ التذكرة</button>
    </form>
</div>

<?php
include '../../includes/footer.php';
renderFooter();
?>