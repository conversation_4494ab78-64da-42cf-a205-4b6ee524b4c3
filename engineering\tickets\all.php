<?php
session_start();
include '../../includes/auth_engineering.php';
include '../../includes/header.php';
renderHeader("كل التذاكر - الهندسة", false, "../index.php");
include '../../includes/ticket_nav.php';
include '../../config/db.php';

// جلب التذاكر من قاعدة البيانات - فقط التذاكر الموجهة إلى الهندسة
$tickets = [];
$sql = "SELECT id, description, content, source, status FROM tickets WHERE forwarded_to = 'الهندسة' ORDER BY id DESC";
$result = $conn->query($sql);
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $tickets[] = $row;
    }
}
?>

<div class="container py-4">
    <h3>📬 تذاكر قسم الهندسة</h3>
    <a href="add.php" class="btn btn-success mb-3">➕ إضافة تذكرة جديدة</a>
    <table class="table table-striped table-dark mt-3">
        <thead>
            <tr>
                <th>#</th>
                <th>الوصف</th>
                <th>مضمون التكت</th>
                <th>موجهة من</th>
                <th>الحالة</th>
                <th>عرض</th>
                <th>طباعة</th>
            </tr>
        </thead>
        <tbody>
            <?php if (count($tickets) > 0): ?>
                <?php foreach ($tickets as $ticket): ?>
                    <tr>
                        <td><?= $ticket['id'] ?></td>
                        <td><?= htmlspecialchars($ticket['description']) ?></td>
                        <td><?= htmlspecialchars(mb_strimwidth($ticket['content'], 0, 40, "...")) ?></td>
                        <td><?= htmlspecialchars($ticket['source'] ?? 'غير محدد') ?></td>
                        <td>
                            <?php if ($ticket['status'] == 'مفتوحة'): ?>
                                <span class="badge bg-warning text-dark">مفتوحة</span>
                            <?php else: ?>
                                <span class="badge bg-secondary">مغلقة</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <a href="view.php?id=<?= $ticket['id'] ?>" class="btn btn-sm btn-info" style="font-weight: bold; min-width: 70px;">عرض</a>
                        </td>
                        <td>
                            <a href="print.php?id=<?= $ticket['id'] ?>" class="btn btn-sm btn-secondary" style="font-weight: bold; min-width: 70px;">طباعة</a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php else: ?>
                <tr>
                    <td colspan="7" class="text-center">لا توجد تذاكر</td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<?php
include '../../includes/footer.php';
renderFooter();
?>








