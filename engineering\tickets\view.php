<?php
session_start();
include '../../includes/auth_engineering.php';
include '../../includes/header.php';
renderHeader("تفاصيل التذكرة - الهندسة", false, "../index.php");
include '../../config/db.php';

// جلب بيانات التذكرة
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    echo "<div class='alert alert-danger'>معرّف التذكرة غير صالح.</div>";
    include '../../includes/footer.php';
    renderFooter();
    exit();
}
$ticket_id = intval($_GET['id']);
$stmt = $conn->prepare("SELECT * FROM tickets WHERE id = ? AND forwarded_to = 'الهندسة'");
$stmt->bind_param("i", $ticket_id);
$stmt->execute();
$ticket = $stmt->get_result()->fetch_assoc();
if (!$ticket) {
    echo "<div class='alert alert-danger'>التذكرة غير موجودة أو غير موجهة إلى قسم الهندسة.</div>";
    include '../../includes/footer.php';
    renderFooter();
    exit();
}

// معالجة الرد فقط (إزالة معالجة الإغلاق)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // إضافة رد
    if (isset($_POST['reply']) && !empty($_POST['reply'])) {
        $reply = $_POST['reply'];
        $ar_name = $_SESSION['user']['ar_name'] ?? 'مستخدم الهندسة';
        $stmt = $conn->prepare("INSERT INTO ticket_replies (ticket_id, reply, ar_name) VALUES (?, ?, ?)");
        $stmt->bind_param("iss", $ticket_id, $reply, $ar_name);
        $stmt->execute();
        header("Location: view.php?id=$ticket_id");
        exit();
    }
}

// جلب الردود
$replies = [];
$res = $conn->prepare("SELECT * FROM ticket_replies WHERE ticket_id = ? ORDER BY created_at ASC");
$res->bind_param("i", $ticket_id);
$res->execute();
$replies_result = $res->get_result();
while ($row = $replies_result->fetch_assoc()) {
    $replies[] = $row;
}
?>

<style>
    .gov-ticket-card {
        max-width: 800px;
        margin: 30px auto;
        background: #23272b;
        border-radius: 15px;
        box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        padding: 30px;
        color: #fff;
    }
    .gov-ticket-card h3 {
        color: #1DA1F2;
        margin-bottom: 25px;
        text-align: center;
    }
    .ticket-label {
        font-weight: bold;
        color: #1DA1F2;
        margin-top: 15px;
        margin-bottom: 5px;
    }
    .ticket-value {
        background: #1E2124;
        padding: 12px 15px;
        border-radius: 8px;
        margin-bottom: 10px;
    }
    .gov-ticket-card .ticket-image {
        display: block;
        margin: 18px auto 18px auto;
        max-width: 350px;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(13,110,253,0.13);
        border: 1px solid #e3e6ea;
    }
    .gov-ticket-card .badge {
        font-size: 1rem;
        padding: 6px 16px;
        border-radius: 20px;
    }
    .gov-ticket-card .gov-section-title {
        color: #1DA1F2;
        font-size: 1.2em;
        font-weight: bold;
        margin-top: 30px;
        margin-bottom: 15px;
        border-bottom: 2px solid #1E2124;
        padding-bottom: 5px;
    }
    .gov-ticket-actions {
        display: flex;
        justify-content: center;
        margin-top: 30px;
        gap: 10px;
    }
    .gov-ticket-actions .btn {
        padding: 8px 20px;
        border-radius: 30px;
        font-weight: bold;
    }
    .gov-ticket-actions .btn-primary {
        background: #1DA1F2;
        border: none;
    }
    .gov-ticket-actions .btn-primary:hover {
        background: #1a91da;
    }
</style>

<div class="gov-ticket-card">
    <h3>تفاصيل التذكرة رقم <?= $ticket['id'] ?></h3>
    <?php if (!empty($ticket['image'])): ?>
        <img src="../../assets/images/tickets/<?= htmlspecialchars($ticket['image']) ?>" alt="صورة التذكرة" class="ticket-image">
    <?php endif; ?>

    <div class="gov-section-title">الردود على التذكرة</div>
    <?php if (count($replies) > 0): ?>
        <?php foreach ($replies as $reply): ?>
            <div class="card mb-2" style="background:#1E2124;border-radius:8px;">
                <div class="card-body">
                    <b style="color:#1DA1F2"><?= htmlspecialchars($reply['ar_name']) ?>:</b>
                    <p class="mb-0" style="color:#fff"><?= nl2br(htmlspecialchars($reply['reply'])) ?></p>
                    <small class="text-muted"><?= $reply['created_at'] ?></small>
                </div>
            </div>
        <?php endforeach; ?>
    <?php else: ?>
        <div class="alert alert-info">لا توجد ردود بعد.</div>
    <?php endif; ?>

    <div class="gov-section-title">إضافة رد</div>
    <form method="POST">
        <div class="mb-3">
            <textarea class="form-control bg-dark text-light" id="reply" name="reply" required rows="3" placeholder="اكتب ردك هنا..."></textarea>
        </div>
        <button type="submit" class="btn btn-primary">إرسال الرد</button>
    </form>

    <div class="gov-section-title" style="margin-top:35px;">معلومات التذكرة</div>
    <div class="ticket-label">وصف المشكلة:</div>
    <div class="ticket-value"><?= htmlspecialchars($ticket['description']) ?></div>
    <div class="ticket-label">مضمون التكت:</div>
    <div class="ticket-value"><?= isset($ticket['content']) ? nl2br(htmlspecialchars($ticket['content'])) : '<span class="text-muted">غير متوفر</span>' ?></div>
    <div class="ticket-label">موجهة من:</div>
    <div class="ticket-value"><?= htmlspecialchars($ticket['source'] ?? 'غير محدد') ?></div>
    <div class="ticket-label">الحالة:</div>
    <div class="ticket-value">
        <?php if ($ticket['status'] == 'مفتوحة'): ?>
            <span class="badge bg-warning text-dark">مفتوحة</span>
        <?php elseif ($ticket['status'] == 'مغلقة'): ?>
            <span class="badge bg-success">مغلقة</span>
        <?php elseif ($ticket['status'] == 'قيد المعالجة'): ?>
            <span class="badge bg-info text-dark">قيد المعالجة</span>
        <?php else: ?>
            <span class="badge bg-secondary"><?= htmlspecialchars($ticket['status']) ?></span>
        <?php endif; ?>
    </div>

    <div class="gov-ticket-actions">
        <!-- تمت إزالة زر إغلاق التذكرة -->
        <a href="all.php" class="btn btn-primary">
            <span style="font-size:1.2em;">🔙</span> العودة للقائمة
        </a>
    </div>
</div>

<?php
include '../../includes/footer.php';
renderFooter();
?>

