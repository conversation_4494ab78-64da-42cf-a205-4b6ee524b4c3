# routeros-api
Client API for RouterOS/Mikrotik

This class was originally written by <PERSON> and updated by several contributors.  It aims to give a simple interface to the RouterOS API in PHP.

The old Mikrotik Wiki page is found on archive.org https://web.archive.org/web/20170210102259/http://wiki.mikrotik.com/wiki/API_PHP_class

## Contributors (before moving to Git)
* <PERSON>
* <PERSON> (ben [at] infotechsc [dot] com)
* <PERSON> (http://jeremyj.com)
* Cristian <PERSON> (djcristiandeluxe [at] gmail [dot] com)
* <PERSON> (mmv.rus [at] gmail [dot] com)

## Changelog

Please see git logs.  Version 1.0 through current version have been preserved in this Git repo.

