{"name": "ben-menking/routeros-api", "description": "Client API for RouterOS/Mikrotik", "keywords": ["routeros", "mikrotik"], "type": "library", "support": {"issues": "https://github.com/BenMenking/routeros-api/issues", "wiki": "http://wiki.mikrotik.com/wiki/API_PHP_class", "source": "https://github.com/BenMenking/routeros-api"}, "authors": [{"name": "<PERSON>", "email": "<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "http://jeremyj.com"}, {"name": "<PERSON><PERSON><PERSON>", "url": "djc<PERSON>iandel<PERSON>@gmail.com"}, {"name": "<PERSON>", "url": "<EMAIL>"}], "autoload": {"classmap": ["routeros_api.class.php"]}}