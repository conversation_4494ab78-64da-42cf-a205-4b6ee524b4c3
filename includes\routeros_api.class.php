
<?php

class RouterosAPI
{
    var $debug = false;

    var $error_no;
    var $error_str;

    var $attempts = 5;
    var $delay = 3;
    var $port = 8728;

    var $connected = false;
    var $socket;
    var $timeout = 3; // Connection attempt timeout and data read timeout

    var $responses;
    var $response_handler;

    function debug($text)
    {
        if ($this->debug) {
            echo $text . "\n";
        }
    }

    public function connect($ip, $login, $password, $port = 8728)
    {
        $this->connected = false;
        $this->port = $port;

        for ($i = 1; $i <= $this->attempts; $i++) {
            $this->debug("Connection attempt #$i to $ip:$port...");
            $this->socket = @fsockopen($ip, $port, $this->error_no, $this->error_str, $this->timeout);
            if ($this->socket) {
                socket_set_timeout($this->socket, $this->timeout);
                $this->debug("Connected...");
                break;
            }
            sleep($this->delay);
        }

        if (!$this->socket) {
            $this->debug("Connection failed: $this->error_str ($this->error_no)");
            return false;
        }

        // Login
        $this->write('/login', false);
        $this->write('=name=' . $login, false);
        $this->write('=password=' . $password);
        $this->read();

        if (isset($this->responses[0]) && $this->responses[0] == '!done') {
            $this->connected = true;
            return true;
        }

        fclose($this->socket);
        return false;
    }

    public function disconnect()
    {
        fclose($this->socket);
        $this->connected = false;
    }

    public function write($command, $last = true)
    {
        $len = strlen($command);
        $this->writeLength($len);
        fwrite($this->socket, $command);
        if ($last) {
            fwrite($this->socket, chr(0));
        }
    }

    private function writeLength($len)
    {
        if ($len < 0x80) {
            fwrite($this->socket, chr($len));
        } elseif ($len < 0x4000) {
            $len |= 0x8000;
            fwrite($this->socket, chr(($len >> 8) & 0xFF));
            fwrite($this->socket, chr($len & 0xFF));
        } elseif ($len < 0x200000) {
            $len |= 0xC00000;
            fwrite($this->socket, chr(($len >> 16) & 0xFF));
            fwrite($this->socket, chr(($len >> 8) & 0xFF));
            fwrite($this->socket, chr($len & 0xFF));
        }
    }

    public function read()
    {
        $this->responses = [];
        while (true) {
            $word = $this->readWord();
            if ($word == '') break;
            $this->responses[] = $word;
        }
        return $this->responses;
    }

    private function readWord()
    {
        $len = $this->readLength();
        if ($len == 0) return '';
        return fread($this->socket, $len);
    }

    private function readLength()
    {
        $c = ord(fread($this->socket, 1));
        if ($c < 0x80) return $c;
        elseif (($c & 0xC0) == 0x80) return (($c & 0x3F) << 8) + ord(fread($this->socket, 1));
        elseif (($c & 0xE0) == 0xC0) return (($c & 0x1F) << 16) + (ord(fread($this->socket, 1)) << 8) + ord(fread($this->socket, 1));
        else return 0;
    }

    public function comm($command, $params = [])
    {
        $this->write($command, false);
        foreach ($params as $p) {
            $this->write($p, false);
        }
        $this->write('', true);
        return $this->read();
    }
}
?>
