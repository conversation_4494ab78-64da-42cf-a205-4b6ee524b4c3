<?php
session_start();
include '../includes/auth_lab.php';
include '../includes/header.php';
renderHeader("المخابر - الرصين", true, "dashboard/index.php");
include '../config/db.php';

// جلب إحصائيات المواقف
$total_status_count = 0;
$today_status_count = 0;
$pending_tickets_count = 0;

// التحقق من وجود جدول lab_status
$table_check = $conn->query("SHOW TABLES LIKE 'lab_status'");
if ($table_check && $table_check->num_rows > 0) {
    $res = $conn->query("SELECT COUNT(*) as total FROM lab_status");
    if ($res) {
        $row = $res->fetch_assoc();
        $total_status_count = $row['total'];
    }

    $res2 = $conn->query("SELECT COUNT(*) as today FROM lab_status WHERE DATE(created_at) = CURDATE()");
    if ($res2) {
        $row2 = $res2->fetch_assoc();
        $today_status_count = $row2['today'];
    }
}

// التحقق من وجود جدول tickets
$table_check2 = $conn->query("SHOW TABLES LIKE 'tickets'");
if ($table_check2 && $table_check2->num_rows > 0) {
    $res3 = $conn->query("SELECT COUNT(*) as pending FROM tickets WHERE sender_department = 'المخابر' AND status = 'مفتوحة'");
    if ($res3) {
        $row3 = $res3->fetch_assoc();
        $pending_tickets_count = $row3['pending'];
    }
}
?>

<style>
    body {
        background: linear-gradient(-45deg, #0D1117, #1E2124, #0F1319, #1A1C1F);
        background-size: 400% 400%;
        animation: gradientBG 15s ease infinite;
    }
    @keyframes gradientBG {
        0% {background-position: 0% 50%;}
        50% {background-position: 100% 50%;}
        100% {background-position: 0% 50%;}
    }
    .dashboard-container {
        padding: 30px 0;
    }
    .dashboard-title {
        color: #fff;
        text-shadow: 0 0 10px rgba(29, 161, 242, 0.5);
        margin-bottom: 30px;
    }
    .card-custom {
        background-color: #1E2124;
        border: none;
        border-radius: 15px;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
        height: 100%;
    }
    .card-custom:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(29, 161, 242, 0.4);
    }
    .card-custom h5 {
        color: #1DA1F2;
        font-weight: bold;
    }
    .card-custom p {
        color: #aaa;
    }
</style>

<div class="dashboard-container">
    <div class="d-flex justify-content-end mb-3" style="max-width:900px;margin:0 auto;">
        <a href="/rasseem/logout.php" class="btn btn-outline-danger btn-lg" style="border-radius:30px;">
            <span style="font-size:1.2em;">🚪</span> تسجيل الخروج
        </a>
    </div>
    <h1 class="text-center dashboard-title">مرحبًا <?= htmlspecialchars($user['ar_name'] ?? $user['username'] ?? 'المستخدم') ?> 👋</h1>
    <p class="lead text-center mb-5" style="color: #aaa">لوحة تحكم المخابر - نظام الرصين</p>

    <div class="row justify-content-center mb-4">
        <div class="col-md-4 mb-2">
            <div class="card text-center" style="background: #1DA1F2; color: #fff; border-radius: 15px;">
                <div class="card-body">
                    <div style="font-size:2em;">📊</div>
                    <h5>إجمالي المواقف</h5>
                    <div style="font-size:2em; font-weight:bold;"><?= $total_status_count ?></div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-2">
            <div class="card text-center" style="background: #28a745; color: #fff; border-radius: 15px;">
                <div class="card-body">
                    <div style="font-size:2em;">📅</div>
                    <h5>مواقف اليوم</h5>
                    <div style="font-size:2em; font-weight:bold;"><?= $today_status_count ?></div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-2">
            <div class="card text-center" style="background: #dc3545; color: #fff; border-radius: 15px;">
                <div class="card-body">
                    <div style="font-size:2em;">🎫</div>
                    <h5>التذاكر المعلقة</h5>
                    <div style="font-size:2em; font-weight:bold;"><?= $pending_tickets_count ?></div>
                </div>
            </div>
        </div>
    </div>

    <div class="row g-4">
        <div class="col-md-6 col-lg-4">
            <div class="card card-custom text-center p-4">
                <h5>➕ إضافة موقف</h5>
                <p class="mb-3">سجل الموقف اليومي للوحدة</p>
                <a href="add_status.php" class="btn btn-outline-primary">ابدأ الآن</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-4">
            <div class="card card-custom text-center p-4">
                <h5>📩 إرسال تذكرة</h5>
                <p class="mb-3">ابلغ عن مشكلة تحتاج الدعم الفني</p>
                <a href="send_ticket.php" class="btn btn-outline-success">إرسال تذكرة</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-4">
            <div class="card card-custom text-center p-4">
                <h5>📋 عرض المواقف</h5>
                <p class="mb-3">مراجعة المواقف المسجلة سابقاً</p>
                <a href="view_status.php" class="btn btn-outline-warning">عرض المواقف</a>
            </div>
        </div>
    </div>
</div>

<?php
include '../includes/footer.php';
renderFooter();
?>