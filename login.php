<?php
session_start();
require_once 'config/db.php';

$error = '';

// التحقق من وجود جلسة مستخدم نشطة
if (isset($_SESSION['user'])) {
    // توجيه المستخدم إلى الصفحة المناسبة بناءً على دوره
    $role = $_SESSION['user']['role'];
    if ($role === 'الإدارة العامة') {
        header("Location: dashboard/index.php");
        exit();
    } elseif ($role === 'مدير') {
        header("Location: admin/index.php");
        exit();
    } elseif ($role === 'إدارة الجوال') {
        header("Location: mobile/index.php");
        exit();
    } elseif ($role === 'المخابر') {
        header("Location: lab/index.php");
        exit();
    } elseif ($role === 'الهندسة') {
        header("Location: engineering/index.php");
        exit();
    } elseif ($role === 'الصيانة') {
        header("Location: maintenance/index.php");
        exit();
    }
}

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $conn->real_escape_string($_POST['username']);
    $password = $_POST['password'];
    
    $sql = "SELECT * FROM users WHERE username = '$username'";
    $result = $conn->query($sql);
    
    if ($result->num_rows === 1) {
        $user = $result->fetch_assoc();
        
        // التحقق من كلمة المرور
        if (password_verify($password, $user['password'])) {
            // تخزين معلومات المستخدم في الجلسة
            $_SESSION['user'] = [
                'id' => $user['id'],
                'username' => $user['username'],
                'ar_name' => $user['ar_name'],
                'role' => $user['role']
            ];
            
            // توجيه المستخدم إلى الصفحة المناسبة بناءً على دوره
            if ($user['role'] === 'الإدارة العامة') {
                header("Location: dashboard/index.php");
                exit();
            } elseif ($user['role'] === 'مدير') {
                header("Location: admin/index.php");
                exit();
            } elseif ($user['role'] === 'إدارة الجوال') {
                header("Location: mobile/index.php");
                exit();
            } elseif ($user['role'] === 'المخابر') {
                header("Location: lab/index.php");
                exit();
            } elseif ($user['role'] === 'الهندسة') {
                header("Location: engineering/index.php");
                exit();
            } elseif ($user['role'] === 'الصيانة') {
                header("Location: maintenance/index.php");
                exit();
            } else {
                header("Location: index.php");
                exit();
            }
        } else {
            $error = "كلمة المرور غير صحيحة";
        }
    } else {
        $error = "اسم المستخدم غير موجود";
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - الرصين</title>
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body {
            min-height: 100vh;
            background: linear-gradient(135deg, #0dcaf0 0%, #6610f2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-card {
            background: #fff;
            border-radius: 18px;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.18);
            padding: 2.5rem 2rem 2rem 2rem;
            max-width: 400px;
            width: 100%;
        }
        .login-card .form-label {
            font-weight: 500;
        }
        .login-card .btn-custom {
            background: linear-gradient(90deg, #0dcaf0 0%, #6610f2 100%);
            color: #fff;
            font-weight: bold;
            border: none;
            transition: 0.2s;
        }
        .login-card .btn-custom:hover {
            background: linear-gradient(90deg, #6610f2 0%, #0dcaf0 100%);
            color: #fff;
        }
        .login-logo {
            font-size: 2.8rem;
            color: #6610f2;
            margin-bottom: 1rem;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="login-card mx-auto">
        <div class="login-logo">
            <span>🔐</span>
        </div>
        <h4 class="mb-4 text-center fw-bold">تسجيل الدخول</h4>
        <?php if (!empty($error)): ?>
            <div class="alert alert-danger text-center"><?php echo $error; ?></div>
        <?php endif; ?>
        <form method="POST" autocomplete="off">
            <div class="mb-3 text-start">
                <label for="username" class="form-label">اسم المستخدم</label>
                <input type="text" class="form-control" id="username" name="username" required placeholder="أدخل اسم المستخدم">
            </div>
            <div class="mb-3 text-start">
                <label for="password" class="form-label">كلمة المرور</label>
                <input type="password" class="form-control" id="password" name="password" required placeholder="أدخل كلمة المرور">
            </div>
            <button type="submit" class="btn btn-custom w-100 mt-3">دخول</button>
        </form>
    </div>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
