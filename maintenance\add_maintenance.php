<?php
session_start();
include '../includes/auth_maintenance.php';
include '../includes/header.php';
renderHeader("إضافة تقرير صيانة - الرصين", true, "index.php");
include '../config/db.php';

$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $device_name = $_POST['device_name'] ?? '';
    $device_type = $_POST['device_type'] ?? '';
    $maintenance_type = $_POST['maintenance_type'] ?? '';
    $description = $_POST['description'] ?? '';
    $status = $_POST['status'] ?? '';
    $technician_name = $_POST['technician_name'] ?? '';
    $maintenance_date = $_POST['maintenance_date'] ?? '';
    
    if (empty($device_name) || empty($device_type) || empty($maintenance_type) || empty($description) || empty($status)) {
        $error_message = "جميع الحقول مطلوبة!";
    } else {
        // إنشاء جدول maintenance_reports إذا لم يكن موجوداً
        $create_table = "CREATE TABLE IF NOT EXISTS maintenance_reports (
            id INT AUTO_INCREMENT PRIMARY KEY,
            device_name VARCHAR(255) NOT NULL,
            device_type VARCHAR(100) NOT NULL,
            maintenance_type VARCHAR(100) NOT NULL,
            description TEXT NOT NULL,
            status VARCHAR(50) NOT NULL,
            technician_name VARCHAR(255) NOT NULL,
            maintenance_date DATE NOT NULL,
            created_by VARCHAR(255) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $conn->query($create_table);
        
        $stmt = $conn->prepare("INSERT INTO maintenance_reports (device_name, device_type, maintenance_type, description, status, technician_name, maintenance_date, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        $created_by = $user['username'];
        $stmt->bind_param("ssssssss", $device_name, $device_type, $maintenance_type, $description, $status, $technician_name, $maintenance_date, $created_by);
        
        if ($stmt->execute()) {
            $success_message = "تم إضافة تقرير الصيانة بنجاح!";
            // إعادة تعيين المتغيرات
            $device_name = $device_type = $maintenance_type = $description = $status = $technician_name = $maintenance_date = '';
        } else {
            $error_message = "حدث خطأ أثناء إضافة التقرير!";
        }
        $stmt->close();
    }
}
?>

<style>
    body {
        background: linear-gradient(-45deg, #0D1117, #1E2124, #0F1319, #1A1C1F);
        background-size: 400% 400%;
        animation: gradientBG 15s ease infinite;
        color: #E5E5E5;
    }
    @keyframes gradientBG {
        0% {background-position: 0% 50%;}
        50% {background-position: 100% 50%;}
        100% {background-position: 0% 50%;}
    }
    .form-container {
        background-color: #1E2124;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        margin: 2rem auto;
        max-width: 800px;
    }
    .form-control, .form-select {
        background-color: #2D3035;
        border: 1px solid #404040;
        color: #E5E5E5;
    }
    .form-control:focus, .form-select:focus {
        background-color: #2D3035;
        border-color: #1DA1F2;
        color: #E5E5E5;
        box-shadow: 0 0 0 0.2rem rgba(29, 161, 242, 0.25);
    }
    .form-label {
        color: #1DA1F2;
        font-weight: bold;
    }
    h2 {
        color: #fff;
        text-shadow: 0 0 10px rgba(29, 161, 242, 0.5);
        margin-bottom: 2rem;
    }
</style>

<div class="container">
    <div class="form-container">
        <h2 class="text-center">إضافة تقرير صيانة جديد</h2>
        
        <?php if ($success_message): ?>
            <div class="alert alert-success"><?= $success_message ?></div>
        <?php endif; ?>
        
        <?php if ($error_message): ?>
            <div class="alert alert-danger"><?= $error_message ?></div>
        <?php endif; ?>
        
        <form method="POST">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="device_name" class="form-label">اسم الجهاز</label>
                    <input type="text" class="form-control" id="device_name" name="device_name" value="<?= htmlspecialchars($device_name ?? '') ?>" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="device_type" class="form-label">نوع الجهاز</label>
                    <select class="form-select" id="device_type" name="device_type" required>
                        <option value="">اختر نوع الجهاز</option>
                        <option value="راديو" <?= (isset($device_type) && $device_type === 'راديو') ? 'selected' : '' ?>>راديو</option>
                        <option value="حاسوب" <?= (isset($device_type) && $device_type === 'حاسوب') ? 'selected' : '' ?>>حاسوب</option>
                        <option value="طابعة" <?= (isset($device_type) && $device_type === 'طابعة') ? 'selected' : '' ?>>طابعة</option>
                        <option value="شبكة" <?= (isset($device_type) && $device_type === 'شبكة') ? 'selected' : '' ?>>معدات شبكة</option>
                        <option value="أخرى" <?= (isset($device_type) && $device_type === 'أخرى') ? 'selected' : '' ?>>أخرى</option>
                    </select>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="maintenance_type" class="form-label">نوع الصيانة</label>
                    <select class="form-select" id="maintenance_type" name="maintenance_type" required>
                        <option value="">اختر نوع الصيانة</option>
                        <option value="صيانة دورية" <?= (isset($maintenance_type) && $maintenance_type === 'صيانة دورية') ? 'selected' : '' ?>>صيانة دورية</option>
                        <option value="إصلاح عطل" <?= (isset($maintenance_type) && $maintenance_type === 'إصلاح عطل') ? 'selected' : '' ?>>إصلاح عطل</option>
                        <option value="تحديث" <?= (isset($maintenance_type) && $maintenance_type === 'تحديث') ? 'selected' : '' ?>>تحديث</option>
                        <option value="استبدال قطع" <?= (isset($maintenance_type) && $maintenance_type === 'استبدال قطع') ? 'selected' : '' ?>>استبدال قطع</option>
                        <option value="فحص شامل" <?= (isset($maintenance_type) && $maintenance_type === 'فحص شامل') ? 'selected' : '' ?>>فحص شامل</option>
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="status" class="form-label">حالة الصيانة</label>
                    <select class="form-select" id="status" name="status" required>
                        <option value="">اختر الحالة</option>
                        <option value="مكتملة" <?= (isset($status) && $status === 'مكتملة') ? 'selected' : '' ?>>مكتملة</option>
                        <option value="قيد التنفيذ" <?= (isset($status) && $status === 'قيد التنفيذ') ? 'selected' : '' ?>>قيد التنفيذ</option>
                        <option value="مؤجلة" <?= (isset($status) && $status === 'مؤجلة') ? 'selected' : '' ?>>مؤجلة</option>
                        <option value="تحتاج قطع غيار" <?= (isset($status) && $status === 'تحتاج قطع غيار') ? 'selected' : '' ?>>تحتاج قطع غيار</option>
                    </select>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="technician_name" class="form-label">اسم الفني</label>
                    <input type="text" class="form-control" id="technician_name" name="technician_name" value="<?= htmlspecialchars($technician_name ?? '') ?>" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="maintenance_date" class="form-label">تاريخ الصيانة</label>
                    <input type="date" class="form-control" id="maintenance_date" name="maintenance_date" value="<?= htmlspecialchars($maintenance_date ?? '') ?>" required>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="description" class="form-label">وصف الصيانة</label>
                <textarea class="form-control" id="description" name="description" rows="4" required><?= htmlspecialchars($description ?? '') ?></textarea>
            </div>
            
            <div class="text-center">
                <button type="submit" class="btn btn-primary btn-lg">إضافة التقرير</button>
                <a href="index.php" class="btn btn-secondary btn-lg ms-2">العودة</a>
            </div>
        </form>
    </div>
</div>

<?php
include '../includes/footer.php';
renderFooter();
?>
