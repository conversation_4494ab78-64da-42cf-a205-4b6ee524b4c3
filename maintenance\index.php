<?php
session_start();
include '../includes/auth_maintenance.php';
include '../includes/header.php';
renderHeader("الصيانة - الرصين", true, "dashboard/index.php");
include '../config/db.php';

// جلب إحصائيات الصيانة
$total_maintenance_count = 0;
$today_maintenance_count = 0;
$pending_tickets_count = 0;

// التحقق من وجود جدول maintenance_reports
$table_check = $conn->query("SHOW TABLES LIKE 'maintenance_reports'");
if ($table_check && $table_check->num_rows > 0) {
    $res = $conn->query("SELECT COUNT(*) as total FROM maintenance_reports");
    if ($res) {
        $row = $res->fetch_assoc();
        $total_maintenance_count = $row['total'];
    }

    $res2 = $conn->query("SELECT COUNT(*) as today FROM maintenance_reports WHERE DATE(created_at) = CURDATE()");
    if ($res2) {
        $row2 = $res2->fetch_assoc();
        $today_maintenance_count = $row2['today'];
    }
}

// التحقق من وجود جدول tickets
$table_check2 = $conn->query("SHOW TABLES LIKE 'tickets'");
if ($table_check2 && $table_check2->num_rows > 0) {
    $res3 = $conn->query("SELECT COUNT(*) as pending FROM tickets WHERE sender_department = 'الصيانة' AND status = 'مفتوحة'");
    if ($res3) {
        $row3 = $res3->fetch_assoc();
        $pending_tickets_count = $row3['pending'];
    }
}
?>

<style>
    body {
        background: linear-gradient(-45deg, #0D1117, #1E2124, #0F1319, #1A1C1F);
        background-size: 400% 400%;
        animation: gradientBG 15s ease infinite;
    }
    @keyframes gradientBG {
        0% {background-position: 0% 50%;}
        50% {background-position: 100% 50%;}
        100% {background-position: 0% 50%;}
    }
    .dashboard-container {
        padding: 30px 0;
    }
    .dashboard-title {
        color: #fff;
        text-shadow: 0 0 10px rgba(29, 161, 242, 0.5);
        margin-bottom: 30px;
    }
    .card-custom {
        background-color: #1E2124;
        border: none;
        border-radius: 15px;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
        height: 100%;
    }
    .card-custom:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(29, 161, 242, 0.4);
    }
    .card-custom h5 {
        color: #1DA1F2;
        font-weight: bold;
    }
    .card-custom p {
        color: #aaa;
    }
</style>

<div class="dashboard-container">
    <div class="d-flex justify-content-end mb-3" style="max-width:900px;margin:0 auto;">
        <a href="/rasseem/logout.php" class="btn btn-outline-danger btn-lg" style="border-radius:30px;">
            <span style="font-size:1.2em;">🚪</span> تسجيل الخروج
        </a>
    </div>
    <h1 class="text-center dashboard-title">مرحبًا <?= isset($_SESSION['user']['ar_name']) && !empty($_SESSION['user']['ar_name']) ? htmlspecialchars($_SESSION['user']['ar_name']) : htmlspecialchars($_SESSION['user']['username']) ?> 👋</h1>
    <p class="lead text-center mb-5" style="color: #aaa">لوحة تحكم الصيانة - نظام الرصين</p>
    
    <div class="row justify-content-center mb-4">
        <div class="col-md-4 mb-2">
            <div class="card text-center" style="background: #1DA1F2; color: #fff; border-radius: 15px;">
                <div class="card-body">
                    <div style="font-size:2em;">🔧</div>
                    <h5>إجمالي التقارير</h5>
                    <div style="font-size:2em; font-weight:bold;"><?= $total_maintenance_count ?></div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-2">
            <div class="card text-center" style="background: #28a745; color: #fff; border-radius: 15px;">
                <div class="card-body">
                    <div style="font-size:2em;">📅</div>
                    <h5>تقارير اليوم</h5>
                    <div style="font-size:2em; font-weight:bold;"><?= $today_maintenance_count ?></div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-2">
            <div class="card text-center" style="background: #dc3545; color: #fff; border-radius: 15px;">
                <div class="card-body">
                    <div style="font-size:2em;">🎫</div>
                    <h5>التذاكر المعلقة</h5>
                    <div style="font-size:2em; font-weight:bold;"><?= $pending_tickets_count ?></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row g-4">
        <div class="col-md-6 col-lg-4">
            <div class="card card-custom text-center p-4">
                <h5>🔧 إضافة تقرير صيانة</h5>
                <p class="mb-3">سجل تقرير صيانة جديد للأجهزة</p>
                <a href="add_maintenance.php" class="btn btn-outline-primary">إضافة تقرير</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-4">
            <div class="card card-custom text-center p-4">
                <h5>📩 إرسال تذكرة</h5>
                <p class="mb-3">ابلغ عن مشكلة تحتاج الدعم الفني</p>
                <a href="send_ticket.php" class="btn btn-outline-success">إرسال تذكرة</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-4">
            <div class="card card-custom text-center p-4">
                <h5>📋 عرض التقارير</h5>
                <p class="mb-3">مراجعة تقارير الصيانة السابقة</p>
                <a href="view_maintenance.php" class="btn btn-outline-warning">عرض التقارير</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-4">
            <div class="card card-custom text-center p-4">
                <h5>🛠️ جدولة الصيانة</h5>
                <p class="mb-3">جدولة مهام الصيانة الدورية</p>
                <a href="schedule_maintenance.php" class="btn btn-outline-info">جدولة المهام</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-4">
            <div class="card card-custom text-center p-4">
                <h5>📊 إحصائيات الصيانة</h5>
                <p class="mb-3">عرض إحصائيات وتقارير الأداء</p>
                <a href="maintenance_stats.php" class="btn btn-outline-secondary">عرض الإحصائيات</a>
            </div>
        </div>
        <div class="col-md-6 col-lg-4">
            <div class="card card-custom text-center p-4">
                <h5>🔍 فحص الأجهزة</h5>
                <p class="mb-3">فحص حالة الأجهزة والمعدات</p>
                <a href="device_inspection.php" class="btn btn-outline-danger">فحص الأجهزة</a>
            </div>
        </div>
    </div>
</div>

<?php
include '../includes/footer.php';
renderFooter();
?>
