<?php
session_start();
include '../includes/auth_maintenance.php';
include '../includes/header.php';
renderHeader("إرسال تذكرة - الرصين", true, "index.php");
include '../config/db.php';

$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = $_POST['title'] ?? '';
    $description = $_POST['description'] ?? '';
    $priority = $_POST['priority'] ?? '';
    $category = $_POST['category'] ?? '';
    
    if (empty($title) || empty($description) || empty($priority) || empty($category)) {
        $error_message = "جميع الحقول مطلوبة!";
    } else {
        $stmt = $conn->prepare("INSERT INTO tickets (title, description, priority, category, sender_department, sender_name, status, created_at) VALUES (?, ?, ?, ?, ?, ?, 'مفتوحة', NOW())");
        $sender_department = 'الصيانة';
        $sender_name = $user['ar_name'] ?? $user['username'];
        $stmt->bind_param("ssssss", $title, $description, $priority, $category, $sender_department, $sender_name);
        
        if ($stmt->execute()) {
            $success_message = "تم إرسال التذكرة بنجاح!";
            $title = $description = $priority = $category = '';
        } else {
            $error_message = "حدث خطأ أثناء إرسال التذكرة!";
        }
        $stmt->close();
    }
}
?>

<style>
    body {
        background: linear-gradient(-45deg, #0D1117, #1E2124, #0F1319, #1A1C1F);
        background-size: 400% 400%;
        animation: gradientBG 15s ease infinite;
        color: #E5E5E5;
    }
    @keyframes gradientBG {
        0% {background-position: 0% 50%;}
        50% {background-position: 100% 50%;}
        100% {background-position: 0% 50%;}
    }
    .form-container {
        background-color: #1E2124;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        margin: 2rem auto;
        max-width: 800px;
    }
    .form-control, .form-select {
        background-color: #2D3035;
        border: 1px solid #404040;
        color: #E5E5E5;
    }
    .form-control:focus, .form-select:focus {
        background-color: #2D3035;
        border-color: #1DA1F2;
        color: #E5E5E5;
        box-shadow: 0 0 0 0.2rem rgba(29, 161, 242, 0.25);
    }
    .form-label {
        color: #1DA1F2;
        font-weight: bold;
    }
    h2 {
        color: #fff;
        text-shadow: 0 0 10px rgba(29, 161, 242, 0.5);
        margin-bottom: 2rem;
    }
</style>

<div class="container">
    <div class="form-container">
        <h2 class="text-center">إرسال تذكرة دعم فني</h2>
        
        <?php if ($success_message): ?>
            <div class="alert alert-success"><?= $success_message ?></div>
        <?php endif; ?>
        
        <?php if ($error_message): ?>
            <div class="alert alert-danger"><?= $error_message ?></div>
        <?php endif; ?>
        
        <form method="POST">
            <div class="mb-3">
                <label for="title" class="form-label">عنوان التذكرة</label>
                <input type="text" class="form-control" id="title" name="title" value="<?= htmlspecialchars($title ?? '') ?>" required>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="category" class="form-label">فئة المشكلة</label>
                    <select class="form-select" id="category" name="category" required>
                        <option value="">اختر الفئة</option>
                        <option value="مشكلة تقنية" <?= (isset($category) && $category === 'مشكلة تقنية') ? 'selected' : '' ?>>مشكلة تقنية</option>
                        <option value="طلب صيانة" <?= (isset($category) && $category === 'طلب صيانة') ? 'selected' : '' ?>>طلب صيانة</option>
                        <option value="طلب قطع غيار" <?= (isset($category) && $category === 'طلب قطع غيار') ? 'selected' : '' ?>>طلب قطع غيار</option>
                        <option value="استفسار فني" <?= (isset($category) && $category === 'استفسار فني') ? 'selected' : '' ?>>استفسار فني</option>
                        <option value="تحديث نظام" <?= (isset($category) && $category === 'تحديث نظام') ? 'selected' : '' ?>>تحديث نظام</option>
                        <option value="أخرى" <?= (isset($category) && $category === 'أخرى') ? 'selected' : '' ?>>أخرى</option>
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="priority" class="form-label">الأولوية</label>
                    <select class="form-select" id="priority" name="priority" required>
                        <option value="">اختر الأولوية</option>
                        <option value="عالية" <?= (isset($priority) && $priority === 'عالية') ? 'selected' : '' ?>>عالية</option>
                        <option value="متوسطة" <?= (isset($priority) && $priority === 'متوسطة') ? 'selected' : '' ?>>متوسطة</option>
                        <option value="منخفضة" <?= (isset($priority) && $priority === 'منخفضة') ? 'selected' : '' ?>>منخفضة</option>
                    </select>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="description" class="form-label">وصف المشكلة</label>
                <textarea class="form-control" id="description" name="description" rows="5" placeholder="اكتب وصفاً مفصلاً للمشكلة..." required><?= htmlspecialchars($description ?? '') ?></textarea>
            </div>
            
            <div class="text-center">
                <button type="submit" class="btn btn-success btn-lg">إرسال التذكرة</button>
                <a href="index.php" class="btn btn-secondary btn-lg ms-2">العودة</a>
            </div>
        </form>
    </div>
</div>

<?php
include '../includes/footer.php';
renderFooter();
?>
