<?php
session_start();
include '../includes/auth_maintenance.php';
include '../includes/header.php';
renderHeader("عرض تقارير الصيانة - الرصين", true, "index.php");
include '../config/db.php';

// جلب تقارير الصيانة
$maintenance_reports = [];
$table_check = $conn->query("SHOW TABLES LIKE 'maintenance_reports'");
if ($table_check && $table_check->num_rows > 0) {
    $result = $conn->query("SELECT * FROM maintenance_reports ORDER BY created_at DESC");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $maintenance_reports[] = $row;
        }
    }
}
?>

<style>
    body {
        background: linear-gradient(-45deg, #0D1117, #1E2124, #0F1319, #1A1C1F);
        background-size: 400% 400%;
        animation: gradientBG 15s ease infinite;
        color: #E5E5E5;
    }
    @keyframes gradientBG {
        0% {background-position: 0% 50%;}
        50% {background-position: 100% 50%;}
        100% {background-position: 0% 50%;}
    }
    .container {
        padding: 2rem 0;
    }
    .card {
        background-color: #1E2124;
        border: none;
        border-radius: 15px;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        margin-bottom: 1rem;
    }
    .card-header {
        background-color: #2D3035;
        border-bottom: 1px solid #404040;
        border-radius: 15px 15px 0 0 !important;
    }
    .table-dark {
        background-color: #1E2124;
    }
    .table-dark th {
        color: #1DA1F2;
        border-color: #404040;
    }
    .table-dark td {
        color: #E5E5E5;
        border-color: #404040;
    }
    h2 {
        color: #fff;
        text-shadow: 0 0 10px rgba(29, 161, 242, 0.5);
        margin-bottom: 2rem;
    }
    .badge {
        font-size: 0.8em;
    }
</style>

<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>تقارير الصيانة</h2>
        <a href="add_maintenance.php" class="btn btn-primary">إضافة تقرير جديد</a>
    </div>
    
    <?php if (empty($maintenance_reports)): ?>
        <div class="card">
            <div class="card-body text-center">
                <h5>لا توجد تقارير صيانة</h5>
                <p class="text-muted">لم يتم إضافة أي تقارير صيانة بعد.</p>
                <a href="add_maintenance.php" class="btn btn-primary">إضافة أول تقرير</a>
            </div>
        </div>
    <?php else: ?>
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">جميع تقارير الصيانة</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-dark table-striped">
                        <thead>
                            <tr>
                                <th>اسم الجهاز</th>
                                <th>نوع الجهاز</th>
                                <th>نوع الصيانة</th>
                                <th>الحالة</th>
                                <th>الفني</th>
                                <th>تاريخ الصيانة</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($maintenance_reports as $report): ?>
                                <tr>
                                    <td><?= htmlspecialchars($report['device_name']) ?></td>
                                    <td><?= htmlspecialchars($report['device_type']) ?></td>
                                    <td><?= htmlspecialchars($report['maintenance_type']) ?></td>
                                    <td>
                                        <?php
                                        $status_class = '';
                                        switch ($report['status']) {
                                            case 'مكتملة':
                                                $status_class = 'bg-success';
                                                break;
                                            case 'قيد التنفيذ':
                                                $status_class = 'bg-warning';
                                                break;
                                            case 'مؤجلة':
                                                $status_class = 'bg-secondary';
                                                break;
                                            case 'تحتاج قطع غيار':
                                                $status_class = 'bg-danger';
                                                break;
                                            default:
                                                $status_class = 'bg-info';
                                        }
                                        ?>
                                        <span class="badge <?= $status_class ?>"><?= htmlspecialchars($report['status']) ?></span>
                                    </td>
                                    <td><?= htmlspecialchars($report['technician_name']) ?></td>
                                    <td><?= date('Y-m-d', strtotime($report['maintenance_date'])) ?></td>
                                    <td><?= date('Y-m-d H:i', strtotime($report['created_at'])) ?></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-info" onclick="viewDetails(<?= $report['id'] ?>)">
                                            عرض التفاصيل
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Modal لعرض تفاصيل التقرير -->
<div class="modal fade" id="detailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-dark text-light">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل تقرير الصيانة</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- سيتم تحميل التفاصيل هنا -->
            </div>
        </div>
    </div>
</div>

<script>
function viewDetails(reportId) {
    // البحث عن التقرير في البيانات
    const reports = <?= json_encode($maintenance_reports) ?>;
    const report = reports.find(r => r.id == reportId);
    
    if (report) {
        const modalBody = document.getElementById('modalBody');
        modalBody.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <strong>اسم الجهاز:</strong> ${report.device_name}<br>
                    <strong>نوع الجهاز:</strong> ${report.device_type}<br>
                    <strong>نوع الصيانة:</strong> ${report.maintenance_type}<br>
                    <strong>الحالة:</strong> ${report.status}
                </div>
                <div class="col-md-6">
                    <strong>الفني:</strong> ${report.technician_name}<br>
                    <strong>تاريخ الصيانة:</strong> ${report.maintenance_date}<br>
                    <strong>أنشأ بواسطة:</strong> ${report.created_by}<br>
                    <strong>تاريخ الإنشاء:</strong> ${report.created_at}
                </div>
            </div>
            <hr>
            <div>
                <strong>وصف الصيانة:</strong><br>
                <p class="mt-2">${report.description}</p>
            </div>
        `;
        
        const modal = new bootstrap.Modal(document.getElementById('detailsModal'));
        modal.show();
    }
}
</script>

<?php
include '../includes/footer.php';
renderFooter();
?>
