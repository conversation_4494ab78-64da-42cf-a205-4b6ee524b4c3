<?php
<?php
require('routeros_api.class.php');

class MikrotikControl {
    private $api;
    private $host;
    private $username;
    private $password;

    public function __construct($host, $username, $password) {
        $this->host = $host;
        $this->username = $username;
        $this->password = $password;
        $this->api = new RouterosAPI();
    }

    public function connect() {
        $this->api->debug = false;
        if($this->api->connect($this->host, $this->username, $this->password)) {
            return true;
        }
        return false;
    }

    public function getInterfaces() {
        return $this->api->comm("/interface/print");
    }

    public function getUsers() {
        return $this->api->comm("/ip/hotspot/user/print");
    }

    public function addHotspotUser($name, $password, $profile = "default") {
        return $this->api->comm("/ip/hotspot/user/add", array(
            "name" => $name,
            "password" => $password,
            "profile" => $profile
        ));
    }

    public function disconnect() {
        $this->api->disconnect();
    }
}